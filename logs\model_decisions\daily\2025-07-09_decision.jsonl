{"timestamp": "2025-07-09T00:00:13.766504", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9790928363800049, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:00:13.930380", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:00:13.936512", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:00:13.996860", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7609137296676636, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:00:14.060746", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.47217317228665145, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:00:14.074053", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.9870616793632507, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:00:14.156540", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999897480010986, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:00:14.221895", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:00:14.248469", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:03:13.949803", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9917844533920288, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:03:14.014672", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9758798452018792, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:03:14.020336", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:03:14.078601", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7209064364433289, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:03:14.150875", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.38421951985098096, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:03:14.160607", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.7490527629852295, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:03:14.242825", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999862909317017, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:03:14.310957", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:03:14.316519", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:06:13.969779", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9936618804931641, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:06:14.037674", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9863461150431492, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:06:14.042834", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:06:14.103409", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7430360913276672, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:06:14.167242", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.38943502676400493, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:06:14.176885", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.9777358770370483, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:06:14.257502", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999858140945435, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:06:14.328769", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:06:14.334372", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:09:13.887519", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9013932347297668, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:09:13.951823", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9954761904761904, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:09:13.956918", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:09:14.017677", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7732520699501038, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:09:14.084411", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4858124599311368, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:09:14.094065", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9038947820663452, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:09:14.173545", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999912977218628, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:09:14.240722", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:09:14.246289", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:12:13.804821", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8941336274147034, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:12:13.887686", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:12:13.892857", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:12:13.951289", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7795554399490356, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:12:14.026768", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4731510666597753, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:12:14.035912", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9365344047546387, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:12:14.116510", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999921321868896, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:12:14.193698", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:12:14.198843", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:16:47.194868", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7542375326156616, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:16:47.261190", "model_name": "short_term_momentum_rf", "signal": -2, "confidence": 0.4062402464808466, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:16:47.266294", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:16:47.484583", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5658388733863831, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:16:47.547035", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8584526125476627, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:16:47.557286", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998452663421631, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:16:47.670419", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998732805252075, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:16:47.734870", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:16:47.739974", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:20:18.034503", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7181016802787781, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:20:18.098485", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8829227994227996, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:20:18.104125", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:20:18.323183", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7540172934532166, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:20:18.390638", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7983961017684277, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:20:18.401440", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999713897705078, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:20:18.517567", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999527931213379, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:20:18.580929", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:20:18.586028", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:23:28.235318", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8374489545822144, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:23:28.298663", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8755351662226663, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:23:28.314113", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:23:28.542218", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7779837250709534, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:23:28.606583", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8591598728526764, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:23:28.641769", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999837875366211, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:23:28.762911", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999618530273438, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:23:28.825871", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:23:28.835607", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:26:38.062607", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9623275995254517, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:26:38.127936", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9689299242424242, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:26:38.133127", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:26:38.359246", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7826536893844604, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:26:38.424801", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8629294597472632, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:26:38.435581", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999840259552002, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:26:38.553488", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999613761901855, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:26:38.618480", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:26:38.623679", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:29:49.249894", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8729591965675354, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:29:49.317764", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9659753787878788, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:29:49.323474", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:29:49.545151", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7913532257080078, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:29:49.608320", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8587461321099882, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:29:49.618572", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999659061431885, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:29:49.733054", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999607801437378, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:29:49.796237", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:29:49.801887", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:32:59.560746", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5887847542762756, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:32:59.625927", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8279025831378359, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:32:59.631032", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:32:59.857761", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7405635714530945, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:32:59.920466", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6642172440961773, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:32:59.931165", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999836683273315, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:33:00.052398", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999470710754395, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:33:00.116512", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.55, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:33:00.121612", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:36:09.277166", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7942980527877808, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:36:09.342290", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8768563797313798, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:36:09.347928", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:36:09.565075", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8016707897186279, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:36:09.631264", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7293930048668803, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:36:09.641549", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999837875366211, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:36:09.751113", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.99996018409729, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:36:09.812909", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:36:09.818006", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:39:19.276131", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8725605010986328, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:39:19.341219", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9705163504795858, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:39:19.346852", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:39:19.565600", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.727947473526001, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:39:19.630164", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5447449923893254, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:39:19.640387", "model_name": "medium_term_volatility_xgb", "signal": 2, "confidence": 0.5676852464675903, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:39:19.753492", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999653100967407, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:39:19.816906", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:39:19.822010", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:42:29.433447", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.807405412197113, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:42:29.500891", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.984702380952381, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:42:29.505965", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:42:29.727375", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7311425805091858, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:42:29.790610", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4823008116777629, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:42:29.800793", "model_name": "medium_term_volatility_xgb", "signal": 2, "confidence": 0.6913886070251465, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:42:29.916657", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999706745147705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:42:29.980887", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:42:29.985989", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:45:39.368300", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8013682961463928, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:45:39.434000", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9935416666666668, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:45:39.439118", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:45:39.656269", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7515064477920532, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:45:39.723631", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.46935206241786553, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:45:39.733791", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.6534545421600342, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:45:39.847425", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999746084213257, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:45:39.910064", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:45:39.915670", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:48:49.224487", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7800711989402771, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:48:49.290969", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.941851371825056, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:48:49.296064", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:48:49.519889", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6160293221473694, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:48:49.582797", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4414504206183776, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:48:49.592949", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999386072158813, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:48:49.706844", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999884366989136, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:48:49.769101", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:48:49.774208", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:52:00.048140", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7575643658638, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:52:00.115540", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7684774733085945, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:52:00.121180", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:52:00.349364", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5891929268836975, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:52:00.413462", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6813705724611238, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:52:00.423737", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999017715454102, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:52:00.542395", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999980926513672, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:52:00.604923", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:52:00.610083", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:55:09.409042", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7804502844810486, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:55:09.474306", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9951643356643358, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:55:09.481075", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:55:09.706929", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.634979248046875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:55:09.771297", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6320888760022821, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:55:09.781975", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.5915483236312866, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:55:09.895016", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999668598175049, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:55:09.960032", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:55:09.965168", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T00:58:19.645731", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7853442430496216, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T00:58:19.710849", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T00:58:19.716415", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T00:58:19.939135", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7956530451774597, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T00:58:20.002320", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6130203981166943, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T00:58:20.013071", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8488509058952332, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T00:58:20.128514", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T00:58:20.192091", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T00:58:20.197745", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:01:29.212082", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.968794584274292, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:01:29.277975", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7193829456749206, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:01:29.283618", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:01:29.507690", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5183079838752747, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:01:29.569682", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8212788674349857, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:01:29.579370", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999893069267273, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:01:29.696910", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999951958656311, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:01:29.760641", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:01:29.765742", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:04:39.312574", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9859822988510132, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:04:39.377881", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9655136345136345, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:04:39.383520", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:04:39.606017", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7791088223457336, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:04:39.669345", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8049102464231972, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:04:39.680159", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998874664306641, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:04:39.795748", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999494552612305, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:04:39.858362", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:04:39.863506", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:07:49.289639", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9783501625061035, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:07:49.356761", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9640120214752567, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:07:49.361888", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:07:49.590913", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7880704998970032, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:07:49.655393", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7714747097633855, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:07:49.666071", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999135732650757, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:07:49.780728", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999908208847046, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:07:49.846769", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:07:49.851840", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:10:59.436289", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9935659766197205, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:10:59.499421", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9893154761904762, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:10:59.505065", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:10:59.728080", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7856268882751465, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:10:59.792880", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7714747097633855, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:10:59.803114", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998761415481567, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:10:59.921451", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999990701675415, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:10:59.984945", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:10:59.990071", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:14:09.270546", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9943625926971436, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:14:09.338396", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9991666666666668, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:14:09.344037", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:14:09.566913", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7863743305206299, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:14:09.631419", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8754869242389018, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:14:09.641599", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999961256980896, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:14:09.753487", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:14:09.818409", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:14:09.823465", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:17:19.262472", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9515870213508606, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:17:19.328881", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9692710722975323, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:17:19.334483", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:17:19.557270", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.69832843542099, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:17:19.621851", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.35160543315417697, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:17:19.632121", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999239444732666, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:17:19.749093", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999634027481079, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:17:19.812878", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:17:19.818500", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:20:29.376784", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.590226411819458, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:20:29.442983", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.981375, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:20:29.448645", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:20:29.680850", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6902876496315002, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:20:29.747032", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.30786362155639135, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:20:29.758346", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999262094497681, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:20:29.872863", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999638795852661, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:20:29.938559", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:20:29.944242", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:23:39.290580", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.968018114566803, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:23:39.355987", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9987301587301587, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:23:39.361596", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:23:39.580737", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6174149513244629, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:23:39.646178", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4089018879478209, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:23:39.656328", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9279939532279968, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:23:39.767645", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999887943267822, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:23:39.829173", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:23:39.834290", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:26:50.028363", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9934787750244141, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:26:50.096683", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:26:50.101818", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:26:50.325991", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7538390159606934, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:26:50.387692", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.3884810534646705, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:26:50.397435", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8299145698547363, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:26:50.513949", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999899864196777, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:26:50.580194", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:26:50.585355", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:29:59.501278", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9931907057762146, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:29:59.565775", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9989285714285714, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:29:59.570883", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:29:59.797722", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7842783331871033, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:29:59.863442", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4039356402252901, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:29:59.874195", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.5921061038970947, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:29:59.989553", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.99998939037323, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:30:00.055505", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:30:00.060668", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:33:09.143200", "model_name": "short_term_pattern_nn", "signal": -1, "confidence": 0.254683256149292, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:33:09.206427", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9955731158220366, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:33:09.212034", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:33:09.432782", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.658318817615509, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:33:09.496702", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.45895248820821477, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:33:09.507251", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9113320112228394, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:33:09.623248", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999896287918091, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:33:09.687178", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:33:09.692233", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:36:19.097604", "model_name": "short_term_pattern_nn", "signal": 0, "confidence": 0.5169333219528198, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:36:19.163617", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:36:19.168724", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:36:19.390774", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6879950165748596, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:36:19.457837", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6147887511283074, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:36:19.468105", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8479650020599365, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:36:19.578678", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999948740005493, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:36:19.644091", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:36:19.649250", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:39:29.169710", "model_name": "short_term_pattern_nn", "signal": 0, "confidence": 0.49461936950683594, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:39:29.235743", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:39:29.254655", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:39:29.479061", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.719253659248352, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:39:29.541983", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5361856026000865, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:39:29.551749", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.7281869649887085, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:39:29.669166", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999947547912598, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:39:29.733077", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:39:29.759701", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:42:39.106298", "model_name": "short_term_pattern_nn", "signal": 0, "confidence": 0.568040132522583, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:42:39.169783", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:42:39.175369", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:42:39.390440", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8003634214401245, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:42:39.462649", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.39461118789024974, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:42:39.472919", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.6409985423088074, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:42:39.588596", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999958276748657, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:42:39.656151", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:42:39.661779", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:45:49.211927", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9741611480712891, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:45:49.275965", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8866040338195288, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:45:49.286221", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:45:49.512306", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.27488675713539124, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:45:49.574190", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4309092736921093, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:45:49.583919", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9983153343200684, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:45:49.715320", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998838901519775, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:45:49.778734", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:45:49.786936", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:48:59.381544", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.49111029505729675, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:48:59.445969", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9623948024668364, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:48:59.451072", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:48:59.666385", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7102795243263245, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:48:59.730421", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4390934875224491, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:48:59.746837", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9958400726318359, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:48:59.860838", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999922513961792, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:48:59.945678", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:48:59.950795", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:52:09.508390", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7356926202774048, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:52:09.574803", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9702582417582417, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:52:09.615243", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:52:09.856980", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7616103291511536, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:52:09.948996", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4615277623924813, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:52:10.007779", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9571076035499573, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:52:10.129513", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999947547912598, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:52:10.194544", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:52:10.208339", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:55:19.246520", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7376856207847595, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:55:19.309983", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.999375, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:55:19.315092", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:55:19.535558", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.758823573589325, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:55:19.608731", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5344098923778027, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:55:19.618935", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9350850582122803, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:55:19.728770", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999945163726807, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:55:19.802387", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:55:19.807541", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T01:58:29.530277", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7380239367485046, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T01:58:29.596751", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T01:58:29.602945", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T01:58:29.838924", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7658149003982544, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T01:58:29.907382", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5346488675870122, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T01:58:29.917630", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.996601939201355, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T01:58:30.030183", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999991655349731, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T01:58:30.093130", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T01:58:30.098758", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:06:19.499128", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6179615259170532, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:06:19.562621", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9899994578017548, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:06:19.568251", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:06:19.788527", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5741267204284668, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:06:19.852518", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.477756675441112, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:06:19.863234", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9995771050453186, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:06:19.977851", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999698400497437, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:06:20.042234", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:06:20.047369", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:09:29.541491", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9076094031333923, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:09:29.608729", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9927285659256, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:09:29.614408", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:09:29.832366", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6470445990562439, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:09:29.907091", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.440195630561205, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:09:29.916764", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998216032981873, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:09:30.026979", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999755620956421, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:09:30.090274", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:09:30.095403", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:12:39.663663", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9904519319534302, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:12:39.731199", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9924036796536797, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:12:39.736772", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:12:39.957008", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7484817504882812, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:12:40.021353", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.358668724616625, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:12:40.031066", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999467134475708, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:12:40.146159", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999830722808838, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:12:40.210411", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:12:40.215539", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:15:49.586177", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9909805655479431, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:15:49.653284", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9990000000000001, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:15:49.658406", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:15:49.878771", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.741491436958313, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:15:49.942695", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.34911115326588615, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:15:49.952961", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999319314956665, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:15:50.067636", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999984622001648, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:15:50.130551", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:15:50.135687", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:18:59.787220", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6278710961341858, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:18:59.852178", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9324860001109999, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:18:59.857803", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:19:00.075280", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5741326808929443, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:19:00.139684", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.9500684342810338, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:19:00.150409", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999432563781738, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:19:00.269533", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999394416809082, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:19:00.334882", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:19:00.340535", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:22:09.755110", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9944676160812378, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:22:09.818947", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8740087108694974, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:22:09.824104", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:22:10.043652", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.49180543422698975, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:22:10.106212", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.7154019105388172, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:22:10.116410", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999241828918457, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:22:10.236314", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999967813491821, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:22:10.298795", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.625, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:22:10.303918", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:25:19.383121", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8912847638130188, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:25:19.447779", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9822982390118696, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:25:19.453379", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:25:19.675555", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8483317494392395, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:25:19.739289", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.7333184597094998, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:25:19.749606", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999758005142212, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:25:19.862735", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999637603759766, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:25:19.927627", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:25:19.932818", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:28:29.760699", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9936180114746094, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:28:29.826868", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9903409090909091, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:28:29.832455", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:28:30.074051", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8493604063987732, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:28:30.137853", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.6866700981034853, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:28:30.149586", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999415874481201, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:28:30.265105", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999643564224243, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:28:30.327646", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:28:30.332750", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:31:39.720303", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9938920736312866, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:31:39.784689", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.48077907632616357, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:31:39.789825", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:31:40.009735", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8524040579795837, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:31:40.073701", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.340812658055847, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:31:40.083457", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999394416809082, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:31:40.204246", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999550580978394, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:31:40.268468", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:31:40.273592", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:34:49.477585", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9938229322433472, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:34:49.543623", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8893751597439663, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:34:49.549267", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:34:49.767494", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8493890762329102, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:34:49.832446", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.329938942238173, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:34:49.842678", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999643564224243, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:34:49.958337", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999574422836304, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:34:50.020117", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:34:50.025230", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:37:59.507704", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9580426216125488, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:37:59.573284", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9961489675516225, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:37:59.578948", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:37:59.795519", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8341881036758423, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:37:59.859612", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7717486689278061, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:37:59.869875", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999513626098633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:37:59.983894", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999974250793457, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:38:00.051001", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:38:00.056116", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:41:09.738573", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.946999192237854, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:41:09.804364", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9960416666666666, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:41:09.809508", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:41:10.032379", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8411335349082947, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:41:10.095905", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4744588121835015, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:41:10.106154", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999514818191528, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:41:10.219214", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999740123748779, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:41:10.282079", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:41:10.287214", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:44:19.357885", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9620838165283203, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:44:19.422339", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:44:19.428006", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:44:19.653827", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8287574052810669, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:44:19.720456", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.605010513078811, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:44:19.730634", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999399185180664, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:44:19.844791", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:44:19.919371", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.62, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:44:19.924992", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:47:29.382410", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5054476857185364, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:47:29.446197", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8627294372294374, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:47:29.451826", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:47:29.671277", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.542721688747406, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:47:29.737149", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8787049562417232, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:47:29.747909", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999624490737915, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:47:29.865326", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9986942410469055, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:47:29.929154", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:47:29.934238", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:50:39.484611", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7446984648704529, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:50:39.551735", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9597512626262628, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:50:39.556796", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:50:39.789254", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7680677771568298, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:50:39.854259", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8214670415543427, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:50:39.865474", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999716281890869, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:50:39.979394", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999444484710693, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:50:40.042328", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:50:40.047904", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:53:49.771726", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8032907843589783, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:53:49.836598", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.930176191863692, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:53:49.841741", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:53:50.074221", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8060968518257141, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:53:50.138013", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9192884670785512, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:53:50.148801", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999829530715942, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:53:50.266223", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999632835388184, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:53:50.330128", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.625, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:53:50.335188", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T02:56:59.535954", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.602179765701294, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T02:56:59.600523", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9851561147186146, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T02:56:59.606147", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T02:56:59.834278", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.817571222782135, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T02:56:59.899223", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9228297591198431, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T02:56:59.909928", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999613761901855, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T02:57:00.031784", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999628067016602, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T02:57:00.095173", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T02:57:00.100872", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:00:09.782834", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9282262325286865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:00:09.851388", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9824375, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:00:09.857011", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:00:10.076777", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8091900944709778, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:00:10.142262", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9020334614401176, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:00:10.153023", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999659061431885, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:00:10.271185", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:00:10.334017", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:00:10.339147", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:03:19.504820", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7859833240509033, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:03:19.570404", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8886854771045948, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:03:19.575515", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:03:19.795847", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.813859760761261, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:03:19.860877", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4741492774672246, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:03:19.871130", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999809265136719, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:03:19.984566", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999614953994751, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:03:20.047371", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:03:20.052519", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:06:29.675479", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7782304883003235, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:06:29.743669", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9192065909706585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:06:29.748743", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:06:29.973327", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8171712756156921, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:06:30.037197", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6716002357098404, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:06:30.048394", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999761581420898, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:06:30.163756", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999765157699585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:06:30.226047", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:06:30.231163", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:09:39.567085", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7701160311698914, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:09:39.633075", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9754730408480411, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:09:39.638212", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:09:39.854852", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.628634512424469, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:09:39.921328", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3944919409680704, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:09:39.932068", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.9618518352508545, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:09:40.043989", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999669790267944, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:09:40.107848", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:09:40.113005", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:12:49.554985", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7711763381958008, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:12:49.620632", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9962835497835499, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:12:49.625736", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:12:49.848105", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7944547533988953, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:12:49.913616", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.42461934910741744, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:12:49.923880", "model_name": "medium_term_volatility_xgb", "signal": 2, "confidence": 0.9830508828163147, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:12:50.044608", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999711513519287, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:12:50.107487", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:12:50.112593", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:15:59.559431", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.765815258026123, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:15:59.625117", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9988035714285713, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:15:59.630748", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:15:59.848500", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.782686173915863, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:15:59.911479", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4324803701742754, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:15:59.921736", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.814300537109375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:16:00.035902", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999975323677063, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:16:00.100885", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:16:00.105996", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:19:09.703014", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.778567910194397, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:19:09.769623", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9660537977229465, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:19:09.775244", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:19:09.995499", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6407094597816467, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:19:10.060022", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.41263176582815503, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:19:10.070224", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9990410208702087, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:19:10.185049", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999966621398926, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:19:10.247902", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:19:10.252970", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:22:19.787640", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9927160143852234, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:22:19.853958", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7536947424762112, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:22:19.859548", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:22:20.089300", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5339523553848267, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:22:20.156992", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5191594996750726, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:22:20.167200", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.5082791447639465, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:22:20.281769", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999920129776001, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:22:20.344966", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:22:20.350151", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:25:29.788980", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.956559419631958, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:25:29.855989", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9983766233766234, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:25:29.861680", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:25:30.080910", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7072914242744446, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:25:30.146911", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.47889841444899484, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:25:30.157254", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.5208151340484619, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:25:30.270425", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999908208847046, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:25:30.335295", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:25:30.340954", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:28:39.588289", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9932849407196045, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:28:39.655268", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.999090909090909, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:28:39.660403", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:28:39.886561", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7924747467041016, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:28:39.949999", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5273263717033393, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:28:39.960286", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.5558344125747681, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:28:40.075429", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999991774559021, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:28:40.137686", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:28:40.142925", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:31:50.008271", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7770734429359436, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:31:50.075470", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8533772518477483, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:31:50.080538", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:31:50.301707", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7462859153747559, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:31:50.367790", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4631228340092662, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:31:50.378501", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.7891431450843811, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:31:50.496881", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999690055847168, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:31:50.559404", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:31:50.564975", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:34:59.769571", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9708533883094788, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:34:59.838507", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9722277334152334, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:34:59.844124", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:35:00.071764", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6565613746643066, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:35:00.134427", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.44363802659590307, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:35:00.144671", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9153798818588257, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:35:00.260703", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999878406524658, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:35:00.324494", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:35:00.331688", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:38:09.694353", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9956952333450317, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:38:09.762295", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9730060169613829, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:38:09.767980", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:38:09.985887", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7491476535797119, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:38:10.050888", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5392886901352626, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:38:10.061127", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.7449646592140198, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:38:10.176068", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999921321868896, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:38:10.240527", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:38:10.246185", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:41:19.579503", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9972761273384094, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:41:19.645638", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.998952380952381, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:41:19.650765", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:41:19.870982", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.745890200138092, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:41:19.935924", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5363097296790255, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:41:19.946147", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.7520771622657776, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:41:20.060240", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999920129776001, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:41:20.124083", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:41:20.129210", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:44:29.530366", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.99733966588974, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:44:29.595229", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9992857142857143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:44:29.600352", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:44:29.818676", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8084397912025452, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:44:29.881377", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5988986092346616, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:44:29.892123", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.7599985003471375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:44:30.006435", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999909400939941, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:44:30.069924", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:44:30.075031", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:47:39.617075", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8414425849914551, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:47:39.685172", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9739904401154403, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:47:39.690783", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:47:39.913575", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5354679226875305, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:47:39.977942", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6124568224779016, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:47:39.988688", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999001026153564, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:47:40.105708", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999258518218994, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:47:40.169047", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:47:40.174171", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:50:50.021884", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.833943784236908, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:50:50.086848", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9892521367521369, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:50:50.091986", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:50:50.313821", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.735440194606781, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:50:50.376757", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5785601302889927, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:50:50.387524", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998124241828918, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:50:50.504578", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999499320983887, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:50:50.567438", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:50:50.573048", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:53:59.676222", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5387381315231323, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:53:59.740762", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9896349206349206, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:53:59.745907", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:53:59.967232", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8016795516014099, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:54:00.033260", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6350063226630595, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:54:00.043540", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999642372131348, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:54:00.157206", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999734163284302, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:54:00.222220", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:54:00.227873", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T03:57:09.591595", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.885290265083313, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T03:57:09.679732", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9991666666666668, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T03:57:09.684854", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T03:57:09.902974", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7997239232063293, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T03:57:09.966379", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7215880421023804, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T03:57:09.976644", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999352693557739, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T03:57:10.099962", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999731779098511, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T03:57:10.164874", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T03:57:10.170026", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:00:19.722419", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7571904063224792, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:00:19.791518", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:00:19.797091", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:00:20.018711", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8245154023170471, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:00:20.081574", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9132802595591767, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:00:20.091713", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999659061431885, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:00:20.204653", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:00:20.266569", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:00:20.271633", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:03:18.008911", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5841257572174072, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:03:18.311717", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9232489732489734, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:03:18.316824", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:03:18.376802", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8082812428474426, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:03:18.439282", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5276311877548115, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:03:18.448946", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999657869338989, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:03:18.525436", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999563694000244, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:03:18.591325", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:03:18.596927", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:06:17.872655", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9328327775001526, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:06:18.069836", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.5896265527219107, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:06:18.074948", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:06:18.136759", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8258756995201111, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:06:18.202364", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8057640656705409, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:06:18.212634", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999947190284729, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:06:18.293757", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999732971191406, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:06:18.360240", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:06:18.365857", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:09:17.823134", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8448411822319031, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:09:18.079830", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9874166666666667, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:09:18.663985", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:09:18.732988", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7367109060287476, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:09:18.795376", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.35276827783015635, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:09:18.806647", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999712705612183, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:09:18.916055", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:09:18.977388", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:09:18.983039", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:12:17.880342", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9476514458656311, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:12:17.956417", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9991666666666668, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:12:17.962039", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:12:18.021452", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7357879877090454, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:12:18.108573", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.42519788726696583, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:12:18.117752", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999940037727356, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:12:18.193601", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:12:18.289474", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:12:18.295115", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:15:18.089574", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9492069482803345, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:15:18.156543", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9991666666666668, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:15:18.162683", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:15:18.221689", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7346360683441162, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:15:18.335335", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.43137053930612906, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:15:18.345057", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999274015426636, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:15:18.423179", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:15:18.490297", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:15:18.495940", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:18:17.958240", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.638410747051239, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:18:18.031778", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9685858585858586, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:18:18.037411", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:18:18.099115", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8017911314964294, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:18:18.163842", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8954802109478767, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:18:18.173523", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999566078186035, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:18:18.253181", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999436140060425, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:18:18.321206", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:18:18.326324", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:21:17.821954", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.992152988910675, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:21:17.903508", "model_name": "short_term_momentum_rf", "signal": 2, "confidence": 0.502685552393336, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:21:17.908677", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:21:17.966388", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.817690372467041, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:21:18.029398", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8958854712881182, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:21:18.038778", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999690055847168, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:21:18.115661", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999723434448242, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:21:18.181293", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:21:18.186391", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:24:17.803596", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8092242479324341, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:24:18.678503", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9951904761904763, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:24:18.683683", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:24:18.741447", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8181341886520386, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:24:18.805517", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.89866457404936, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:24:18.815652", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999785423278809, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:24:18.892741", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999656677246094, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:24:18.958700", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:24:18.963717", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:27:17.740321", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8552180528640747, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:27:18.150586", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:27:18.155775", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:27:18.211866", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.821743369102478, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:27:18.273382", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9289813764356636, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:27:18.298749", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999688863754272, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:27:18.371951", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999632835388184, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:27:18.436427", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.57, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:27:18.445236", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:30:17.824685", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8686223030090332, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:30:18.013752", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.998, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:30:18.019597", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:30:18.077338", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8256868720054626, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:30:18.141007", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9343735671419903, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:30:18.150551", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999696016311646, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:30:18.226663", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999626874923706, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:30:18.291223", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:30:18.296252", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:33:18.108442", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7003739476203918, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:33:18.176543", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9527231240981239, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:33:18.180979", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:33:18.237268", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8103647828102112, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:33:18.309762", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8187021346763643, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:33:18.319305", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999738931655884, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:33:18.392405", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999512434005737, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:33:18.483558", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:33:18.487576", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:36:17.737797", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9840369820594788, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:36:17.894704", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.47709921890564594, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:36:18.490251", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:36:18.558931", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8152912259101868, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:36:18.612582", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8196032903252772, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:36:18.622818", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999731779098511, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:36:18.702096", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999880790710449, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:36:18.766841", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:36:18.771308", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:39:17.742357", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6739040017127991, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:39:18.114013", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9927361111111113, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:39:18.118031", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:39:18.173466", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8068214654922485, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:39:18.235904", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8341448578901252, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:39:18.260861", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999734163284302, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:39:18.334245", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999725818634033, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:39:18.399878", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:39:18.408203", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:42:17.837210", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9618434309959412, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:42:17.913297", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:42:17.918804", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:42:17.976479", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8259947896003723, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:42:18.040771", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9150227582994138, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:42:18.049883", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999589920043945, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:42:18.128563", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999634027481079, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:42:18.194252", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:42:18.199734", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:45:17.913789", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9439293742179871, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:45:17.977494", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.994375, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:45:17.982510", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:45:18.041384", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8246700167655945, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:45:18.103784", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9185994728152644, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:45:18.113500", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999639987945557, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:45:18.193174", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:45:18.258756", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:45:18.264789", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:48:17.768303", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6637506484985352, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:48:17.845942", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9582153679653679, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:48:17.851638", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:48:17.909281", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8083347082138062, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:48:17.972051", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8261164586208118, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:48:17.981485", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999538660049438, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:48:18.056926", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999487400054932, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:48:18.124027", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:48:18.129042", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:51:17.761290", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9798970818519592, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:51:17.844114", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.47322055391103957, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:51:18.169111", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:51:18.236079", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8143129348754883, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:51:18.298037", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6823037756043218, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:51:18.308283", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999954342842102, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:51:18.385892", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999880790710449, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:51:18.450511", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:51:18.455566", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:54:17.806303", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8830721378326416, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:54:17.914397", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9931904761904762, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:54:17.918917", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:54:17.976983", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8213095664978027, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:54:18.040221", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7638949373057861, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:54:18.050159", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999743700027466, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:54:18.129010", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999680519104004, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:54:18.193374", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:54:18.198926", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T04:57:17.762011", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.908729076385498, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T04:57:18.123187", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T04:57:18.127785", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T04:57:18.183798", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8243359923362732, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T04:57:18.247277", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8677412206409749, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T04:57:18.269678", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999591112136841, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T04:57:18.341795", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999632835388184, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T04:57:18.419642", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T04:57:18.430830", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:00:17.831660", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8846789598464966, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:00:18.883170", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9958571428571428, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:00:18.887711", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:00:18.945437", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8274745941162109, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:00:19.007280", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8749535222282765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:00:19.016307", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999589920043945, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:00:19.093450", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999626874923706, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:00:19.158508", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:00:19.163520", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:03:17.817272", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7629676461219788, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:03:17.960399", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9441713009213007, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:03:17.965428", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:03:18.021295", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6676950454711914, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:03:18.083765", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.44356211065529527, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:03:18.108519", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999538660049438, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:03:18.180756", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999505281448364, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:03:18.245670", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:03:18.254065", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:06:17.891850", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7705304622650146, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:06:17.956447", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9310144300144302, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:06:17.961833", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:06:18.020450", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7402280569076538, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:06:18.082634", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.51604072260192, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:06:18.091178", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999961256980896, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:06:18.168924", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999526739120483, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:06:18.235333", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:06:18.253335", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:09:17.968928", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7691248655319214, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:09:18.035664", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9876873082759389, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:09:18.237473", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:09:18.295853", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.821132481098175, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:09:18.361502", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.4166843505599469, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:09:18.370005", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999762773513794, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:09:18.446639", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999685287475586, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:09:18.512316", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:09:18.516889", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:12:17.814318", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7217728495597839, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:12:17.878745", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9992012195121953, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:12:17.883760", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:12:17.942499", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8285393714904785, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:12:18.003965", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.3991387592199689, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:12:18.013549", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999520778656006, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:12:18.089598", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999634027481079, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:12:18.156431", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:12:18.161852", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:15:29.682277", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.964526891708374, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:15:29.745501", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9921071428571429, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:15:29.751843", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:15:29.963649", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8504822850227356, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:15:30.025016", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.33003513853541944, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:15:30.035489", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999568462371826, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:15:30.150565", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:15:30.212921", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:15:30.217440", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:18:39.554722", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9567725658416748, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:18:39.620314", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9391939935064936, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:18:39.626280", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:18:39.845228", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.820143461227417, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:18:39.908930", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8660878845044279, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:18:39.919787", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999146461486816, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:18:40.032578", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999467134475708, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:18:40.096297", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:18:40.101319", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:21:49.566815", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9728767275810242, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:21:49.630739", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.43134136570947534, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:21:49.663895", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:21:49.884211", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8275680541992188, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:21:49.948303", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.884859687130755, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:21:49.957921", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999680519104004, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:21:50.072299", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999798536300659, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:21:50.151447", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:21:50.156496", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:24:59.431921", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9336109161376953, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:24:59.500668", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9861309523809524, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:24:59.506014", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:24:59.713271", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8238798379898071, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:24:59.775120", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9053066233781598, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:24:59.799772", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999738931655884, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:24:59.916672", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999618530273438, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:24:59.995858", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:25:00.001449", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:28:09.458944", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9760066270828247, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:28:09.536334", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:28:09.541440", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:28:09.748416", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8238514065742493, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:28:09.834262", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9064788107158321, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:28:09.844529", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999583959579468, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:28:09.956523", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999630451202393, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:28:10.030064", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:28:10.034111", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:31:19.280279", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9901537299156189, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:31:19.341460", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9958333333333335, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:31:19.361433", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:31:19.569786", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8261023759841919, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:31:19.632380", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7844164161861851, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:31:19.664246", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:31:19.794621", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999643564224243, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:31:19.856744", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.57, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:31:19.870442", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:34:29.329069", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9841741323471069, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:34:29.403802", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9958333333333335, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:34:29.409350", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:34:29.617330", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8271802067756653, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:34:29.678720", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7331793541190479, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:34:29.711431", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999775886535645, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:34:29.829252", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999608993530273, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:34:29.889804", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.57, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:34:29.905263", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:37:39.251569", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9961552023887634, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:37:39.330327", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:37:39.334843", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:37:39.541291", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7722868919372559, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:37:39.601729", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8848571703204106, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:37:40.015078", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.9763202667236328, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:37:40.156832", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999877214431763, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:37:40.219652", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.57, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:37:40.233801", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:40:49.273045", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9961227774620056, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:40:49.348945", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:40:49.354528", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:40:49.561730", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7855269908905029, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:40:49.657812", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8428680100034613, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:40:49.667828", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.9412999153137207, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:40:49.776496", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999910593032837, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:40:49.844185", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.57, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:40:49.849249", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:43:59.396044", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9963571429252625, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:43:59.468233", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:43:59.473397", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:43:59.682876", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7958362698554993, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:43:59.744280", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8149110672392164, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:43:59.772472", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.8451847434043884, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:43:59.885643", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999993085861206, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:43:59.965498", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:43:59.970539", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:47:09.329932", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5212140679359436, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:47:09.392640", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9954166666666667, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:47:09.398149", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:47:09.607511", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.35384127497673035, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:47:09.671461", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4561889204699506, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:47:09.685411", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999586343765259, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:47:09.799845", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999121427536011, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:47:09.884448", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:47:09.889518", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:50:19.473029", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8095617890357971, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:50:19.534558", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9918654415811412, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:50:19.539580", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:50:19.750235", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5277859568595886, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:50:19.820643", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4490149697444631, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:50:19.831140", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999957799911499, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:50:19.942948", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999417066574097, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:50:20.020519", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:50:20.025157", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:53:29.434841", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4745320677757263, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:53:29.544839", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9890864675516223, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:53:29.550892", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:53:29.757270", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6491673588752747, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:53:29.818214", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5937542646430208, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:53:29.837410", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999542236328125, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:53:29.956405", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999731779098511, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:53:30.019180", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:53:30.033789", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:56:39.363549", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.7794716954231262, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:56:39.424372", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:56:39.429772", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:56:39.638863", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8403812050819397, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:56:39.700648", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5916111270156534, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:56:39.720806", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:56:39.832897", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:56:39.895972", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:56:39.902112", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T05:59:49.432419", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9956483244895935, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T05:59:49.498251", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T05:59:49.503403", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T05:59:49.711443", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.839260458946228, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T05:59:49.790503", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4853270287884293, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T05:59:49.799651", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999686479568481, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T05:59:49.912175", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T05:59:49.982198", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.595, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T05:59:49.986542", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:02:59.698183", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7499340772628784, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:02:59.765878", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9602861215783944, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:02:59.771994", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:02:59.989371", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.847658634185791, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:03:00.053262", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5046647900019048, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:03:00.062317", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999427795410156, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:03:00.173173", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:03:00.239020", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:03:00.244037", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:06:09.375255", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9955730438232422, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:06:09.438480", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7857573742061621, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:06:09.443989", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:06:09.657092", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7491090297698975, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:06:09.719195", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5033167159864598, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:06:09.728827", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998857975006104, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:06:09.840530", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999955892562866, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:06:09.902857", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:06:09.908015", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:09:19.581796", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9930287003517151, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:09:19.647401", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9984642857142856, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:09:19.651560", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:09:19.866787", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8409549593925476, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:09:19.932115", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8420076607006649, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:09:19.941684", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998544454574585, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:09:20.055973", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999736547470093, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:09:20.118044", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:09:20.123551", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:12:29.730806", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9939437508583069, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:12:29.796127", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9995454545454545, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:12:29.801360", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:12:30.016872", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8393172025680542, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:12:30.078886", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7660869426071595, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:12:30.089406", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999698400497437, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:12:30.199441", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:12:30.261661", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:12:30.267071", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:15:39.398528", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9901589751243591, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:15:39.460868", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.999875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:15:39.467135", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:15:39.677523", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8410771489143372, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:15:39.738703", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.75841255480101, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:15:39.749436", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999698400497437, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:15:39.860697", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999725818634033, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:15:39.924033", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:15:39.929175", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:18:49.426339", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5081862807273865, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:18:49.488577", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9589879614052343, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:18:49.493100", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:18:49.712741", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8485684990882874, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:18:49.775978", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4467511624533303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:18:49.785134", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999425411224365, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:18:49.900657", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999494552612305, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:18:49.963816", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:18:49.969910", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:21:59.662026", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9970957040786743, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:21:59.728477", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9114796378379718, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:21:59.734053", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:21:59.949337", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.709989070892334, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:22:00.011876", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9114129145987064, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:22:00.023194", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998961687088013, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:22:00.137778", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999954700469971, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:22:00.202719", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:22:00.208106", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:25:09.664866", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9569208025932312, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:25:09.730139", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9984642857142856, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:25:09.735692", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:25:09.951916", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8414282202720642, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:25:10.017435", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8358703158246005, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:25:10.027342", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998544454574585, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:25:10.142936", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999736547470093, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:25:10.205172", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:25:10.210219", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:28:19.459782", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.965552031993866, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:28:19.525041", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9912012195121952, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:28:19.530090", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:28:19.748517", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8516819477081299, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:28:19.814341", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.46066038201266196, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:28:19.824574", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999698400497437, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:28:19.939142", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999632835388184, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:28:20.001327", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:28:20.006909", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:31:29.658067", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.794757604598999, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:31:29.725570", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8165502902985666, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:31:29.730747", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:31:29.949305", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8325313329696655, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:31:30.014784", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6934925689011772, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:31:30.024961", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998064637184143, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:31:30.138306", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999805688858032, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:31:30.200263", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:31:30.205797", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:34:39.862276", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6224023103713989, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:34:39.925628", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9810842445389858, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:34:39.929657", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:34:40.149404", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8092380166053772, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:34:40.209795", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6681323164386526, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:34:40.221046", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998328685760498, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:34:40.333968", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999507665634155, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:34:40.395501", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:34:40.401043", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:37:49.996065", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7881642580032349, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:37:50.060059", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9963156342182891, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:37:50.065097", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:37:50.285008", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8305191397666931, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:37:50.348584", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.822020790696258, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:37:50.358744", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998393058776855, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:37:50.469082", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999738931655884, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:37:50.534511", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:37:50.539652", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:40:59.737532", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9683919548988342, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:40:59.802294", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:40:59.807820", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:41:00.023787", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8406181931495667, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:41:00.088441", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.49642893834282154, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:41:00.098129", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998443126678467, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:41:00.209680", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999736547470093, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:41:00.274447", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:41:00.279469", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:44:09.722309", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9674701690673828, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:44:09.787328", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:44:09.792418", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:44:10.006439", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8278810977935791, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:44:10.068977", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6503418309062616, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:44:10.079189", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999701976776123, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:44:10.188469", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:44:10.249553", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:44:10.255954", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:47:19.541207", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5944319367408752, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:47:19.605858", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9834003520425934, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:47:19.611366", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:47:19.823103", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5994666218757629, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:47:19.885746", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.879420659205437, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:47:19.895770", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999289512634277, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:47:20.007211", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999517202377319, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:47:20.069250", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:47:20.074634", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:50:29.737339", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5737656354904175, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:50:29.801603", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9878871527777777, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:50:29.806653", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:50:30.027929", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8216601014137268, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:50:30.090765", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7760760052284528, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:50:30.100844", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999860405921936, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:50:30.219203", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999525547027588, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:50:30.282035", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:50:30.287079", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:53:39.897942", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6948137283325195, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:53:39.963569", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9984642857142856, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:53:39.968644", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:53:40.184581", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8384727835655212, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:53:40.246582", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7734924259730657, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:53:40.257133", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998459815979004, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:53:40.372948", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999738931655884, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:53:40.434323", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:53:40.439850", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:56:49.755892", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9576670527458191, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:56:49.821078", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.997, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:56:49.826105", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:56:50.043988", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8296248912811279, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T06:56:50.107286", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.44457069745108546, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T06:56:50.117463", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998810291290283, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T06:56:50.229851", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999736547470093, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T06:56:50.293590", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T06:56:50.298615", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T06:59:59.678597", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9634336233139038, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T06:59:59.742984", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T06:59:59.748020", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T06:59:59.960202", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8287621736526489, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:00:00.023096", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6958874747868345, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:00:00.034222", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999638795852661, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:00:00.149534", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:00:00.213087", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:00:00.218393", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:03:09.826730", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.606825590133667, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:03:09.892563", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99154120066937, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:03:09.897597", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:03:10.112330", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8347063064575195, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:03:10.175146", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6457666854036277, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:03:10.185759", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999074935913086, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:03:10.299914", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999562501907349, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:03:10.362854", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:03:10.367889", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:06:19.845574", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.990365743637085, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:06:19.909805", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8565984755293712, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:06:19.914505", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:06:20.128137", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8249126672744751, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:06:20.193112", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5355417973187141, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:06:20.202634", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999102354049683, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:06:20.313266", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999881982803345, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:06:20.376634", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:06:20.382144", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:09:29.834207", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9764795899391174, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:09:29.899084", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9988194444444445, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:09:30.034012", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:09:30.244676", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8169336318969727, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:09:30.309368", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.40047028996919914, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:09:30.319028", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.8295505046844482, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:09:30.429962", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999834299087524, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:09:30.492191", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:09:30.497565", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:12:39.682066", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9948959946632385, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:12:39.746163", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:12:39.750694", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:12:39.959372", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8495330810546875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:12:40.035889", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.3790702687948784, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:12:40.046026", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9987311959266663, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:12:40.152978", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999829530715942, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:12:40.221577", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:12:40.226695", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:15:49.576856", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7765646576881409, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:15:49.639134", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9504756776232511, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:15:49.660199", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:15:49.873261", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8449932336807251, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:15:49.937384", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8227834174313703, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:15:49.946707", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998412132263184, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:15:50.057496", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999325275421143, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:15:50.117791", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.58, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:15:50.123952", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:18:59.583553", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8355163931846619, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:18:59.648784", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9927792207792208, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:18:59.659637", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:18:59.870779", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7935706973075867, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:18:59.931828", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.9022106313535622, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:18:59.965660", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999423027038574, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:19:00.083052", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999407529830933, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:19:00.143628", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.58, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:19:00.159603", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:22:09.650259", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9951872825622559, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:22:09.715851", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9989823008849558, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:22:09.720311", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:22:09.927717", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8320941925048828, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:22:09.992446", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8163437690247171, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:22:10.014097", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999275207519531, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:22:10.126197", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999734163284302, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:22:10.188715", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.58, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:22:10.195506", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:25:19.668941", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9976438879966736, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:25:19.734559", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:25:19.743275", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:25:19.958865", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8414939045906067, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:25:20.021380", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6688323982192962, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:25:20.032087", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999240636825562, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:25:20.162932", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999731779098511, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:25:20.226253", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.58, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:25:20.244384", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:28:29.631883", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9975537657737732, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:28:29.695055", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:28:29.709711", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:28:29.926340", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8287217020988464, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:28:29.988456", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.73142233500552, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:28:29.999456", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999956488609314, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:28:30.108939", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:28:30.170865", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.58, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:28:30.176633", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:31:39.573311", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5677804946899414, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:31:39.635149", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9757355577689243, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:31:39.648343", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:31:39.874721", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8280841708183289, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:31:39.935736", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.403375488477675, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:31:39.945400", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999380111694336, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:31:40.055415", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999700784683228, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:31:40.117749", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:31:40.123529", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:34:49.759284", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.936529278755188, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:34:49.821960", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9828302181321508, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:34:49.849896", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:34:50.071936", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8284441232681274, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:34:50.133142", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.40304573540325267, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:34:50.143268", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999208450317383, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:34:50.255461", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999700784683228, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:34:50.315768", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:34:50.321164", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:37:59.753662", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5520408749580383, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:37:59.816325", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.999198717948718, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:37:59.821435", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:38:00.031893", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.851944088935852, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:38:00.101292", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4425121823342367, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:38:00.111978", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.7771344780921936, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:38:00.224007", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999877214431763, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:38:00.300679", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:38:00.305130", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:41:09.750897", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.3957720696926117, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:41:09.819608", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9995833333333333, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:41:09.824661", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:41:10.048943", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8493656516075134, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:41:10.150797", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.41106119197168567, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:41:10.161122", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.8864888548851013, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:41:10.272600", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999908208847046, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:41:10.344417", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:41:10.349867", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:44:19.652673", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.35273295640945435, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:44:19.714768", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9995833333333333, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:44:19.720281", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:44:19.935297", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8467350006103516, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:44:19.997575", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.41657641034787657, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:44:20.008437", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.5101181268692017, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:44:20.122437", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999992847442627, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:44:20.184803", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:44:20.190934", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:47:29.626328", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.3563370108604431, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:47:29.688644", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9753333333333333, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:47:29.693665", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:47:29.910742", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.44779226183891296, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:47:29.977835", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.9118027739684241, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:47:29.988453", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999950647354126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:47:30.100516", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998887777328491, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:47:30.164904", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.565, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:47:30.169922", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:50:39.863071", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5841849446296692, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:50:39.925096", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9821254578754578, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:50:39.930399", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:50:40.143196", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8462770581245422, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:50:40.204554", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.9231455080133105, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:50:40.214642", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999490976333618, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:50:40.331183", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999944806098938, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:50:40.394788", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:50:40.399318", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:53:49.907016", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9950449466705322, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:53:49.971155", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9761353602239908, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:53:49.976681", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:53:50.191874", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8514423966407776, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:53:50.255659", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.8082901816617596, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:53:50.265835", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999954104423523, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:53:50.380006", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999641180038452, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:53:50.443900", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:53:50.450734", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T07:56:59.759560", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.994687557220459, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T07:56:59.822351", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.985840909090909, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T07:56:59.827472", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T07:57:00.044775", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8513429164886475, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T07:57:00.108935", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.8256215161696308, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T07:57:00.119174", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999593496322632, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T07:57:00.228286", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999634027481079, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T07:57:00.290737", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T07:57:00.295757", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-09T08:00:20.524897", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9937817454338074, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-09T08:00:20.655227", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9840909090909089, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-09T08:00:20.753065", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-09T08:00:21.074489", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8503002524375916, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-09T08:00:21.165765", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.7914043629069325, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-09T08:00:21.276162", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-09T08:00:21.452886", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-09T08:00:21.566862", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-09T08:00:21.641234", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
