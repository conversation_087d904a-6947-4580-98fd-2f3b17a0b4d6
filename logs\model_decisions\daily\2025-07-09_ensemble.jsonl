{"timestamp": "2025-07-09T00:00:14.263223", "ensemble_signal": 0, "confidence": 0.8669978609787503, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9790928363800049, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.99875, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7609137296676636, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.47217317228665145, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.9870616793632507, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999897480010986, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:03:14.320570", "ensemble_signal": 1, "confidence": 0.8252032102128036, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9917844533920288, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9758798452018792, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7209064364433289, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.38421951985098096, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.7490527629852295, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999862909317017, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:06:14.338423", "ensemble_signal": 1, "confidence": 0.8550222653188512, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9936618804931641, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9863461150431492, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7430360913276672, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.38943502676400493, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.9777358770370483, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999858140945435, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:09:14.250342", "ensemble_signal": 0, "confidence": 0.8516466242206099, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9013932347297668, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9954761904761904, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7732520699501038, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.4858124599311368, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9038947820663452, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999912977218628, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:12:14.202921", "ensemble_signal": 0, "confidence": 0.8542629171194585, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8941336274147034, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7795554399490356, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.4731510666597753, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9365344047546387, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999921321868896, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:16:47.744049", "ensemble_signal": 0, "confidence": 0.7954985994453342, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7542375326156616, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": -2, "confidence": 0.4062402464808466, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.5658388733863831, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8584526125476627, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998452663421631, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9998732805252075, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:20:18.589597", "ensemble_signal": 0, "confidence": 0.8575957378805724, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7181016802787781, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8829227994227996, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7540172934532166, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7983961017684277, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999713897705078, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999527931213379, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:23:28.841196", "ensemble_signal": 0, "confidence": 0.8794525491558399, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8374489545822144, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8755351662226663, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7779837250709534, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8591598728526764, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999837875366211, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999618530273438, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:26:38.627777", "ensemble_signal": 0, "confidence": 0.9046428509061187, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9623275995254517, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9689299242424242, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7826536893844604, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8629294597472632, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999840259552002, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999613761901855, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:29:49.805509", "ensemble_signal": 0, "confidence": 0.8948844669522688, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8729591965675354, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9659753787878788, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7913532257080078, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8587461321099882, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999659061431885, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999607801437378, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:33:00.125753", "ensemble_signal": 0, "confidence": 0.8190442750529153, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5887847542762756, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8279025831378359, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7405635714530945, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6642172440961773, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999836683273315, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999470710754395, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.55, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:36:09.822057", "ensemble_signal": 1, "confidence": 0.8624624202054071, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7942980527877808, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8768563797313798, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8016707897186279, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7293930048668803, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999837875366211, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.99996018409729, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:39:19.825589", "ensemble_signal": 1, "confidence": 0.8048243841297733, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8725605010986328, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9705163504795858, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.727947473526001, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5447449923893254, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 2, "confidence": 0.5676852464675903, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999653100967407, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:42:29.990040", "ensemble_signal": 1, "confidence": 0.8063233388873826, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.807405412197113, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.984702380952381, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7311425805091858, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4823008116777629, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 2, "confidence": 0.6913886070251465, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999706745147705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:45:39.919246", "ensemble_signal": 0, "confidence": 0.8032441340793803, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8013682961463928, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9935416666666668, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7515064477920532, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.46935206241786553, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.6534545421600342, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999746084213257, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:48:49.778303", "ensemble_signal": 0, "confidence": 0.8154809933951065, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7800711989402771, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.941851371825056, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6160293221473694, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4414504206183776, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999386072158813, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999884366989136, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:52:00.614213", "ensemble_signal": 0, "confidence": 0.8173894206471197, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7575643658638, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7684774733085945, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.5891929268836975, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6813705724611238, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999017715454102, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999980926513672, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:55:09.969359", "ensemble_signal": 0, "confidence": 0.799355278972602, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7804502844810486, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9951643356643358, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.634979248046875, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6320888760022821, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.5915483236312866, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999668598175049, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T00:58:20.201815", "ensemble_signal": 0, "confidence": 0.8447600973801984, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7853442430496216, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7956530451774597, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6130203981166943, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.8488509058952332, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999727010726929, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:01:29.769907", "ensemble_signal": 0, "confidence": 0.8447343324770157, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.968794584274292, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7193829456749206, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.5183079838752747, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8212788674349857, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999893069267273, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999951958656311, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:04:39.867103", "ensemble_signal": 1, "confidence": 0.9011501674372843, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9859822988510132, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9655136345136345, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7791088223457336, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8049102464231972, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998874664306641, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999494552612305, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:07:49.855891", "ensemble_signal": 1, "confidence": 0.8974234856557347, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9783501625061035, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9640120214752567, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7880704998970032, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7714747097633855, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999135732650757, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999908208847046, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:10:59.994125", "ensemble_signal": 1, "confidence": 0.9016499419091539, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9935659766197205, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9893154761904762, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7856268882751465, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7714747097633855, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998761415481567, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999990701675415, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:14:09.827550", "ensemble_signal": 1, "confidence": 0.9144804638329226, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9943625926971436, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9991666666666668, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7863743305206299, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8754869242389018, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999961256980896, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999728202819824, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:17:19.822609", "ensemble_signal": 0, "confidence": 0.8361865436172242, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9515870213508606, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9692710722975323, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.69832843542099, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.35160543315417697, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999239444732666, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999634027481079, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:20:29.948347", "ensemble_signal": 0, "confidence": 0.7916269283502743, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.590226411819458, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.981375, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6902876496315002, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.30786362155639135, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999262094497681, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999638795852661, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.555, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:23:39.838345", "ensemble_signal": 0, "confidence": 0.8312274936926787, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.968018114566803, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9987301587301587, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6174149513244629, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4089018879478209, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9279939532279968, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999887943267822, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:26:50.589429", "ensemble_signal": 0, "confidence": 0.8361892204260307, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9934787750244141, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7538390159606934, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.3884810534646705, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.8299145698547363, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999899864196777, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:30:00.064761", "ensemble_signal": 0, "confidence": 0.8147142586663986, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9931907057762146, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9989285714285714, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7842783331871033, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4039356402252901, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.5921061038970947, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.99998939037323, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:33:09.696323", "ensemble_signal": 0, "confidence": 0.7598721001021983, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -1, "confidence": 0.254683256149292, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9955731158220366, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.658318817615509, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.45895248820821477, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9113320112228394, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999896287918091, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:36:19.653305", "ensemble_signal": 0, "confidence": 0.8030751720918397, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 0, "confidence": 0.5169333219528198, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6879950165748596, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6147887511283074, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.8479650020599365, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999948740005493, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:39:29.774028", "ensemble_signal": 0, "confidence": 0.7820266593605918, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 0, "confidence": 0.49461936950683594, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.719253659248352, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5361856026000865, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.7281869649887085, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999947547912598, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:42:39.665319", "ensemble_signal": 0, "confidence": 0.7737787438829682, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 0, "confidence": 0.568040132522583, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8003634214401245, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.39461118789024974, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.6409985423088074, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999958276748657, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:45:49.802309", "ensemble_signal": 0, "confidence": 0.7916400022556053, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9741611480712891, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8866040338195288, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.27488675713539124, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.4309092736921093, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9983153343200684, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9998838901519775, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:48:59.954358", "ensemble_signal": 0, "confidence": 0.795967779612334, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.49111029505729675, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9623948024668364, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7102795243263245, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4390934875224491, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9958400726318359, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999922513961792, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:52:10.214933", "ensemble_signal": 0, "confidence": 0.8279100994478426, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7356926202774048, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9702582417582417, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7616103291511536, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.4615277623924813, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9571076035499573, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999947547912598, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:55:19.811620", "ensemble_signal": 0, "confidence": 0.8378192493829925, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7376856207847595, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.999375, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.758823573589325, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.5344098923778027, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9350850582122803, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999945163726807, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T01:58:30.102327", "ensemble_signal": 0, "confidence": 0.8466764880644648, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7380239367485046, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7658149003982544, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.5346488675870122, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.996601939201355, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999991655349731, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:06:20.051475", "ensemble_signal": 0, "confidence": 0.8065989897548369, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6179615259170532, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9899994578017548, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.5741267204284668, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.477756675441112, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9995771050453186, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999698400497437, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:09:30.099494", "ensemble_signal": 0, "confidence": 0.8430416607978172, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9076094031333923, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9927285659256, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6470445990562439, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.440195630561205, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998216032981873, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999755620956421, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:12:40.219692", "ensemble_signal": 0, "confidence": 0.8544372728389505, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9904519319534302, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9924036796536797, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7484817504882812, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.358668724616625, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999467134475708, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999830722808838, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:15:50.139781", "ensemble_signal": 0, "confidence": 0.8533888102643935, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9909805655479431, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9990000000000001, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.741491436958313, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.34911115326588615, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999319314956665, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999984622001648, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:19:00.344622", "ensemble_signal": 0, "confidence": 0.8549378325098144, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6278710961341858, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9324860001109999, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.5741326808929443, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.9500684342810338, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999432563781738, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999394416809082, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.61, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:22:10.308030", "ensemble_signal": 0, "confidence": 0.8556226910075171, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9944676160812378, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8740087108694974, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.49180543422698975, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.7154019105388172, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999241828918457, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999967813491821, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.625, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:25:19.936917", "ensemble_signal": 0, "confidence": 0.9066858173304344, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8912847638130188, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9822982390118696, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8483317494392395, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.7333184597094998, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999758005142212, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999637603759766, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:28:30.336850", "ensemble_signal": 0, "confidence": 0.9138772168942673, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9936180114746094, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9903409090909091, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8493604063987732, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.6866700981034853, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999415874481201, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999643564224243, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:31:40.277770", "ensemble_signal": 0, "confidence": 0.8191979943201902, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9938920736312866, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.48077907632616357, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8524040579795837, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.340812658055847, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999394416809082, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999550580978394, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:34:50.029320", "ensemble_signal": 0, "confidence": 0.8630497213638373, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9938229322433472, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8893751597439663, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8493890762329102, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.329938942238173, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999643564224243, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999574422836304, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:38:00.060190", "ensemble_signal": 0, "confidence": 0.9183392842534693, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9580426216125488, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9961489675516225, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8341881036758423, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7717486689278061, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999513626098633, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999974250793457, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:41:10.291290", "ensemble_signal": 0, "confidence": 0.8848398092556036, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.946999192237854, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9960416666666666, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8411335349082947, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4744588121835015, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999514818191528, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999740123748779, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:44:19.928568", "ensemble_signal": 0, "confidence": 0.8906404507553702, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9620838165283203, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8287574052810669, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.605010513078811, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999399185180664, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999728202819824, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.62, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:47:29.938325", "ensemble_signal": 0, "confidence": 0.8164733379075426, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5054476857185364, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8627294372294374, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.542721688747406, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8787049562417232, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999624490737915, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9986942410469055, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:50:40.051956", "ensemble_signal": 0, "confidence": 0.8726555784420142, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7446984648704529, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9597512626262628, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7680677771568298, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8214670415543427, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999716281890869, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999444484710693, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:53:50.339243", "ensemble_signal": 0, "confidence": 0.8981997905386037, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8032907843589783, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.930176191863692, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8060968518257141, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9192884670785512, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999829530715942, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999632835388184, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.625, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T02:57:00.104999", "ensemble_signal": 1, "confidence": 0.8925178475915353, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.602179765701294, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9851561147186146, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.817571222782135, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9228297591198431, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999613761901855, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999628067016602, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:00:10.343276", "ensemble_signal": 1, "confidence": 0.9252017448448895, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9282262325286865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9824375, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8091900944709778, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9020334614401176, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999659061431885, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999629259109497, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:03:20.057145", "ensemble_signal": 1, "confidence": 0.8519577604896904, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7859833240509033, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8886854771045948, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.813859760761261, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.4741492774672246, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999809265136719, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999614953994751, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:06:30.235211", "ensemble_signal": 1, "confidence": 0.8767956497354052, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7782304883003235, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9192065909706585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8171712756156921, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6716002357098404, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999761581420898, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999765157699585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:09:40.117101", "ensemble_signal": 1, "confidence": 0.8261704358664672, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7701160311698914, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9754730408480411, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.628634512424469, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.3944919409680704, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.9618518352508545, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999669790267944, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:12:50.116680", "ensemble_signal": 1, "confidence": 0.84217284530711, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7711763381958008, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9962835497835499, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7944547533988953, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.42461934910741744, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 2, "confidence": 0.9830508828163147, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999711513519287, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.61, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:16:00.110092", "ensemble_signal": 0, "confidence": 0.822117868604595, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.765815258026123, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9988035714285713, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.782686173915863, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4324803701742754, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.814300537109375, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999975323677063, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:19:10.257049", "ensemble_signal": 0, "confidence": 0.8224444666274812, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.778567910194397, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9660537977229465, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6407094597816467, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.41263176582815503, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9990410208702087, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999966621398926, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:22:20.354245", "ensemble_signal": 0, "confidence": 0.7680881503081072, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9927160143852234, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7536947424762112, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.5339523553848267, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5191594996750726, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.5082791447639465, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999920129776001, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:25:30.345004", "ensemble_signal": 0, "confidence": 0.806881268863919, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.956559419631958, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9983766233766234, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7072914242744446, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.47889841444899484, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.5208151340484619, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999908208847046, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:28:40.147557", "ensemble_signal": 0, "confidence": 0.8297780820513141, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9932849407196045, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.999090909090909, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7924747467041016, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5273263717033393, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.5558344125747681, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999991774559021, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:31:50.569027", "ensemble_signal": 0, "confidence": 0.8032190197718773, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7770734429359436, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8533772518477483, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7462859153747559, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.4631228340092662, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.7891431450843811, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999690055847168, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:35:00.336278", "ensemble_signal": 0, "confidence": 0.839849758734033, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9708533883094788, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9722277334152334, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6565613746643066, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.44363802659590307, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9153798818588257, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999878406524658, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:38:10.250258", "ensemble_signal": 0, "confidence": 0.8446771076147092, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9956952333450317, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9730060169613829, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7491476535797119, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5392886901352626, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.7449646592140198, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999921321868896, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:41:20.133327", "ensemble_signal": 0, "confidence": 0.847833021829041, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9972761273384094, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.998952380952381, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.745890200138092, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5363097296790255, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.7520771622657776, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999920129776001, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:44:30.079131", "ensemble_signal": 0, "confidence": 0.8626614226848752, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.99733966588974, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9992857142857143, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8084397912025452, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5988986092346616, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.7599985003471375, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999909400939941, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:47:40.178263", "ensemble_signal": 1, "confidence": 0.8375759230910742, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8414425849914551, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9739904401154403, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.5354679226875305, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6124568224779016, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999001026153564, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999258518218994, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:50:50.577142", "ensemble_signal": 1, "confidence": 0.8579953539195758, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.833943784236908, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9892521367521369, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.735440194606781, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.5785601302889927, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998124241828918, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999499320983887, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:54:00.231946", "ensemble_signal": 0, "confidence": 0.8388884625637968, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5387381315231323, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9896349206349206, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8016795516014099, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6350063226630595, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999642372131348, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999734163284302, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T03:57:10.174109", "ensemble_signal": 1, "confidence": 0.8878529919371554, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.885290265083313, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9991666666666668, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7997239232063293, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7215880421023804, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999352693557739, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999731779098511, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:00:20.275766", "ensemble_signal": 1, "confidence": 0.8977682759292137, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7571904063224792, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8245154023170471, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9132802595591767, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999659061431885, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999629259109497, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:03:18.600975", "ensemble_signal": 0, "confidence": 0.8253565445014047, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5841257572174072, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9232489732489734, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8082812428474426, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.5276311877548115, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999657869338989, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999563694000244, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:06:18.369968", "ensemble_signal": 1, "confidence": 0.8598910184362966, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9328327775001526, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.5896265527219107, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8258756995201111, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8057640656705409, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999947190284729, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999732971191406, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:09:18.987119", "ensemble_signal": 1, "confidence": 0.8340756473244498, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8448411822319031, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9874166666666667, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7367109060287476, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.35276827783015635, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999712705612183, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999972939491272, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:12:18.298688", "ensemble_signal": 0, "confidence": 0.8547462698475257, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9476514458656311, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9991666666666668, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7357879877090454, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.42519788726696583, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999940037727356, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999728202819824, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:15:18.499531", "ensemble_signal": 0, "confidence": 0.856031100924743, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9492069482803345, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9991666666666668, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7346360683441162, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.43137053930612906, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999274015426636, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999727010726929, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:18:18.330435", "ensemble_signal": 0, "confidence": 0.8754630836684592, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.638410747051239, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9685858585858586, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8017911314964294, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8954802109478767, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999566078186035, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999436140060425, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:21:18.189905", "ensemble_signal": 1, "confidence": 0.8648172574665327, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.992152988910675, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 2, "confidence": 0.502685552393336, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.817690372467041, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8958854712881182, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999690055847168, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999723434448242, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:24:18.967970", "ensemble_signal": 1, "confidence": 0.8995730311096537, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8092242479324341, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9951904761904763, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8181341886520386, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.89866457404936, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999785423278809, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999656677246094, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:27:18.458497", "ensemble_signal": 1, "confidence": 0.9084305057140606, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8552180528640747, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.821743369102478, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9289813764356636, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999688863754272, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999632835388184, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.57, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:30:18.301415", "ensemble_signal": 1, "confidence": 0.9101794015989005, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8686223030090332, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.998, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8256868720054626, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9343735671419903, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999696016311646, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999626874923706, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:33:18.491999", "ensemble_signal": 1, "confidence": 0.8713431898757041, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7003739476203918, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9527231240981239, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8103647828102112, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8187021346763643, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999738931655884, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999512434005737, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:36:18.775321", "ensemble_signal": 1, "confidence": 0.8506657285879521, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9840369820594788, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.47709921890564594, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8152912259101868, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8196032903252772, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999731779098511, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999880790710449, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:39:18.421396", "ensemble_signal": 1, "confidence": 0.8741724463898001, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.6739040017127991, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9927361111111113, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8068214654922485, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8341448578901252, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999734163284302, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999725818634033, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:42:18.203254", "ensemble_signal": 1, "confidence": 0.9180869951953683, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9618434309959412, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8259947896003723, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9150227582994138, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999589920043945, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999634027481079, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:45:18.268304", "ensemble_signal": 1, "confidence": 0.915722263512715, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9439293742179871, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.994375, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8246700167655945, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9185994728152644, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999639987945557, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999629259109497, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:48:18.133094", "ensemble_signal": 1, "confidence": 0.8684799302687825, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.6637506484985352, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9582153679653679, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8083347082138062, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8261164586208118, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999538660049438, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999487400054932, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:51:18.459082", "ensemble_signal": 1, "confidence": 0.8344084834740044, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9798970818519592, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.47322055391103957, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8143129348754883, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.6823037756043218, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999954342842102, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999880790710449, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:54:18.202943", "ensemble_signal": 1, "confidence": 0.8912676803166598, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8830721378326416, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9931904761904762, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8213095664978027, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7638949373057861, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999743700027466, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999680519104004, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T04:57:18.436991", "ensemble_signal": 1, "confidence": 0.9067475852472592, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.908729076385498, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8243359923362732, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8677412206409749, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999591112136841, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999632835388184, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:00:19.167536", "ensemble_signal": 1, "confidence": 0.9047650535172195, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8846789598464966, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9958571428571428, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8274745941162109, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8749535222282765, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999589920043945, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999626874923706, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:03:18.269185", "ensemble_signal": 0, "confidence": 0.8198111200477367, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7629676461219788, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9441713009213007, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6676950454711914, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.44356211065529527, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999538660049438, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999505281448364, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:06:18.266897", "ensemble_signal": 0, "confidence": 0.8353030206435608, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7705304622650146, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9310144300144302, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7402280569076538, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.51604072260192, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999961256980896, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999526739120483, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:09:18.520905", "ensemble_signal": 0, "confidence": 0.8393970438527782, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7691248655319214, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9876873082759389, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.821132481098175, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.4166843505599469, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999762773513794, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999685287475586, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:12:18.166382", "ensemble_signal": 0, "confidence": 0.834285251500691, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7217728495597839, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9992012195121953, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8285393714904785, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.3991387592199689, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999520778656006, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999634027481079, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:15:30.221516", "ensemble_signal": 0, "confidence": 0.8552300903757654, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.964526891708374, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9921071428571429, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8504822850227356, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.33003513853541944, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999568462371826, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999629259109497, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:18:40.104329", "ensemble_signal": 1, "confidence": 0.9052287608651499, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9567725658416748, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9391939935064936, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.820143461227417, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8660878845044279, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999146461486816, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999467134475708, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:21:50.160524", "ensemble_signal": 1, "confidence": 0.8535103692523359, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9728767275810242, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.43134136570947534, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8275680541992188, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.884859687130755, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999680519104004, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999798536300659, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:25:00.004831", "ensemble_signal": 1, "confidence": 0.9126515176877368, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9336109161376953, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9861309523809524, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8238798379898071, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9053066233781598, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999738931655884, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999618530273438, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:28:10.038598", "ensemble_signal": 1, "confidence": 0.9190286520623528, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9760066270828247, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8238514065742493, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.9064788107158321, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999583959579468, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999630451202393, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:31:19.884608", "ensemble_signal": 1, "confidence": 0.9073819432482632, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9901537299156189, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9958333333333335, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8261023759841919, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7844164161861851, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999676942825317, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999643564224243, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.57, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:34:29.910852", "ensemble_signal": 1, "confidence": 0.9011450108546476, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9841741323471069, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9958333333333335, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8271802067756653, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.7331793541190479, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999775886535645, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999608993530273, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.57, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:37:40.248586", "ensemble_signal": 1, "confidence": 0.9110674262137025, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9961552023887634, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7722868919372559, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8848571703204106, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.9763202667236328, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999877214431763, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.57, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:40:49.853807", "ensemble_signal": 1, "confidence": 0.9039787040092288, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9961227774620056, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7855269908905029, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8428680100034613, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.9412999153137207, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999910593032837, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.57, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:43:59.974552", "ensemble_signal": 1, "confidence": 0.8919202102661841, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.9963571429252625, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7958362698554993, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 2, "confidence": 0.8149110672392164, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.8451847434043884, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999993085861206, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:47:09.893533", "ensemble_signal": 0, "confidence": 0.7651701433655003, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5212140679359436, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9954166666666667, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.35384127497673035, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4561889204699506, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999586343765259, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999121427536011, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.56, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:50:20.029230", "ensemble_signal": 0, "confidence": 0.8181252496555537, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.8095617890357971, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9918654415811412, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.5277859568595886, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.4490149697444631, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999957799911499, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999417066574097, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:53:30.040116", "ensemble_signal": 0, "confidence": 0.8101630159442658, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.4745320677757263, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9890864675516223, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.6491673588752747, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5937542646430208, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999542236328125, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999731779098511, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:56:39.906234", "ensemble_signal": 0, "confidence": 0.8659899300448953, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 1, "confidence": 0.7794716954231262, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8403812050819397, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5916111270156534, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999728202819824, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999972939491272, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T05:59:49.990602", "ensemble_signal": 0, "confidence": 0.8791863049293195, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9956483244895935, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9975, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.839260458946228, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.4853270287884293, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999686479568481, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999727010726929, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.595, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:03:00.248127", "ensemble_signal": 0, "confidence": 0.8513816494327613, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7499340772628784, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9602861215783944, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.847658634185791, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5046647900019048, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999427795410156, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999488592147827, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:06:09.912032", "ensemble_signal": 0, "confidence": 0.8481819037391936, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9955730438232422, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.7857573742061621, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7491090297698975, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5033167159864598, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998857975006104, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999955892562866, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:09:20.127589", "ensemble_signal": 0, "confidence": 0.9199203654970849, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9930287003517151, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9984642857142856, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8409549593925476, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8420076607006649, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998544454574585, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999736547470093, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:12:30.270373", "ensemble_signal": 0, "confidence": 0.9115372882245316, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9939437508583069, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9995454545454545, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8393172025680542, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7660869426071595, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999698400497437, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999728202819824, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:15:39.933196", "ensemble_signal": 0, "confidence": 0.9216072982069932, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9901589751243591, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.999875, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8410771489143372, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.75841255480101, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999698400497437, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999725818634033, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:18:49.973424", "ensemble_signal": 0, "confidence": 0.8297094981297766, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5081862807273865, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9589879614052343, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8485684990882874, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.4467511624533303, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999425411224365, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999494552612305, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:22:00.211130", "ensemble_signal": 0, "confidence": 0.9149853943637297, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9970957040786743, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9114796378379718, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.709989070892334, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9114129145987064, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998961687088013, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999954700469971, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:25:10.215530", "ensemble_signal": 0, "confidence": 0.9263901453020815, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9569208025932312, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9984642857142856, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8414282202720642, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8358703158246005, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998544454574585, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999736547470093, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:28:20.009938", "ensemble_signal": 0, "confidence": 0.8860031431028332, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.965552031993866, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9912012195121952, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8516819477081299, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.46066038201266196, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999698400497437, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999632835388184, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.705, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:31:30.209862", "ensemble_signal": 0, "confidence": 0.8602353791647456, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.794757604598999, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8165502902985666, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8325313329696655, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6934925689011772, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998064637184143, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999805688858032, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:34:40.405061", "ensemble_signal": 0, "confidence": 0.8539600118004403, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6224023103713989, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9810842445389858, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8092380166053772, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6681323164386526, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998328685760498, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999507665634155, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.605, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:37:50.543070", "ensemble_signal": 0, "confidence": 0.892981400537537, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.7881642580032349, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9963156342182891, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8305191397666931, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.822020790696258, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998393058776855, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999738931655884, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:41:00.283714", "ensemble_signal": 0, "confidence": 0.8783618485462402, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9683919548988342, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8406181931495667, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.49642893834282154, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998443126678467, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999736547470093, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:44:10.258977", "ensemble_signal": 0, "confidence": 0.8939595220929891, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9674701690673828, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8278810977935791, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6503418309062616, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999701976776123, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999728202819824, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:47:20.077637", "ensemble_signal": 0, "confidence": 0.8496222027195458, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5944319367408752, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9834003520425934, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.5994666218757629, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.879420659205437, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999289512634277, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999517202377319, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:50:30.291096", "ensemble_signal": 0, "confidence": 0.8604668265161282, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5737656354904175, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9878871527777777, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8216601014137268, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7760760052284528, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999860405921936, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999525547027588, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:53:40.443363", "ensemble_signal": 0, "confidence": 0.8766736312732184, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.6948137283325195, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9984642857142856, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8384727835655212, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.7734924259730657, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998459815979004, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999738931655884, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T06:56:50.301832", "ensemble_signal": 0, "confidence": 0.8681907676071282, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9576670527458191, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.997, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8296248912811279, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.44457069745108546, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998810291290283, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999736547470093, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:00:00.222418", "ensemble_signal": 0, "confidence": 0.8970021595023812, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9634336233139038, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8287621736526489, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6958874747868345, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999638795852661, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999727010726929, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:03:10.372396", "ensemble_signal": 0, "confidence": 0.8520781232840346, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.606825590133667, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.99154120066937, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8347063064575195, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6457666854036277, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999074935913086, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999562501907349, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:06:20.386174", "ensemble_signal": 0, "confidence": 0.8663685222838925, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.990365743637085, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8565984755293712, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8249126672744751, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.5355417973187141, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999102354049683, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999881982803345, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:09:30.501578", "ensemble_signal": 0, "confidence": 0.8458040526614465, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9764795899391174, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9988194444444445, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8169336318969727, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.40047028996919914, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.8295505046844482, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999834299087524, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:12:40.229698", "ensemble_signal": 0, "confidence": 0.8680236751801276, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9948959946632385, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8495330810546875, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.3790702687948784, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9987311959266663, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999829530715942, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:15:50.127009", "ensemble_signal": 0, "confidence": 0.8860655900335561, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7765646576881409, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9504756776232511, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8449932336807251, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8227834174313703, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9998412132263184, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999325275421143, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.58, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:19:00.174255", "ensemble_signal": 0, "confidence": 0.9004399534913409, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.8355163931846619, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9927792207792208, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.7935706973075867, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.9022106313535622, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999423027038574, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999407529830933, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.58, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:22:10.199030", "ensemble_signal": 0, "confidence": 0.9136120072408088, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9951872825622559, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9989823008849558, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8320941925048828, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.8163437690247171, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999275207519531, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999734163284302, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.58, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:25:20.257791", "ensemble_signal": 0, "confidence": 0.8986518906121188, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9976438879966736, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8414939045906067, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.6688323982192962, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999240636825562, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999731779098511, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.58, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:28:30.201333", "ensemble_signal": 0, "confidence": 0.9041807438755021, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9975537657737732, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 1.0, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8287217020988464, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.73142233500552, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999956488609314, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999728202819824, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.58, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:31:40.129072", "ensemble_signal": 0, "confidence": 0.81665370938919, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5677804946899414, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9757355577689243, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8280841708183289, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -1, "confidence": 0.403375488477675, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999380111694336, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999700784683228, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:34:50.325193", "ensemble_signal": 0, "confidence": 0.8584155402409849, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.936529278755188, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9828302181321508, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8284441232681274, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.40304573540325267, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999208450317383, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999700784683228, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:38:00.309665", "ensemble_signal": 0, "confidence": 0.7997575163135887, "consensus": "moderate", "consensus_strength": 0.7777777777777778, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5520408749580383, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.999198717948718, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.851944088935852, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 0, "confidence": 0.4425121823342367, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.7771344780921936, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999877214431763, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:41:10.353871", "ensemble_signal": 1, "confidence": 0.7919179450538927, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.3957720696926117, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9995833333333333, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8493656516075134, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.41106119197168567, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.8864888548851013, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999908208847046, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:44:20.194232", "ensemble_signal": 1, "confidence": 0.7456375842358809, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.35273295640945435, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9995833333333333, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8467350006103516, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.41657641034787657, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 1, "confidence": 0.5101181268692017, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999992847442627, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.585, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:47:30.173991", "ensemble_signal": 0, "confidence": 0.806233820910908, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.3563370108604431, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9753333333333333, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.44779226183891296, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.9118027739684241, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999950647354126, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9998887777328491, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.565, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:50:40.403333", "ensemble_signal": 0, "confidence": 0.8789584950539293, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.5841849446296692, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9821254578754578, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8462770581245422, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.9231455080133105, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999490976333618, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.999944806098938, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:53:50.454239", "ensemble_signal": 0, "confidence": 0.911758965637168, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9950449466705322, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9761353602239908, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8514423966407776, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.8082901816617596, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999954104423523, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999641180038452, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T07:57:00.298804", "ensemble_signal": 0, "confidence": 0.9147128038289003, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.994687557220459, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.985840909090909, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8513429164886475, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.8256215161696308, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999593496322632, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999634027481079, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.575, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T08:00:21.710395", "ensemble_signal": 0, "confidence": 0.912166967200136, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": -2, "confidence": 0.9937817454338074, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.9840909090909089, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8503002524375916, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": -2, "confidence": 0.7914043629069325, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9999629259109497, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999629259109497, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T08:17:08.410528", "ensemble_signal": 0, "confidence": 0.8468598971535726, "consensus": "moderate", "consensus_strength": 0.6666666666666666, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.7812119126319885, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": 0, "confidence": 0.8823994142109671, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8496828675270081, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.5188075739330507, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.9996809959411621, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999567270278931, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.59, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
{"timestamp": "2025-07-09T08:19:22.622170", "ensemble_signal": 0, "confidence": 0.779052923285798, "consensus": "weak", "consensus_strength": 0.5555555555555556, "total_models": 9, "relevant_models": 9, "target_timeframe": 5, "predictions": {"short_term_pattern_nn": {"prediction": 2, "confidence": 0.5899503827095032, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Pattern recognition for scalping with trend direction awareness"}, "short_term_momentum_rf": {"prediction": -1, "confidence": 0.5771103494447343, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Momentum detection for quick trades with trend direction filtering"}, "short_term_reversion_gb": {"prediction": 1, "confidence": 0.9999998524274585, "weight": 1.0, "timeframe_category": "short_term", "purpose": "Mean reversion scalping with trend context"}, "medium_term_trend_lstm": {"prediction": 0, "confidence": 0.8403587341308594, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Trend continuation detection with multi-timeframe analysis"}, "medium_term_breakout_rf": {"prediction": 1, "confidence": 0.40415846886383017, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Breakout detection with trend direction confirmation"}, "medium_term_volatility_xgb": {"prediction": 0, "confidence": 0.999924898147583, "weight": 0.5, "timeframe_category": "medium_term", "purpose": "Volatility trading with trend-aware positioning"}, "long_term_macro_dnn": {"prediction": 0, "confidence": 0.9999738931655884, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Major trend analysis with macro direction awareness"}, "long_term_levels_rf": {"prediction": 0, "confidence": 0.6, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Support/resistance trading with trend-aware levels"}, "long_term_portfolio_gb": {"prediction": 0, "confidence": 0.9999997306826254, "weight": 0.1, "timeframe_category": "long_term", "purpose": "Portfolio allocation optimization with trend-based sizing"}}}
