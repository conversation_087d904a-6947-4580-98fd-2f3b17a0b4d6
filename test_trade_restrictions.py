#!/usr/bin/env python3
"""
Test script to verify that trade opening restrictions are working correctly.
Only short_term_pattern_nn and medium_term_volatility_xgb should be able to open trades.
"""

import sys
import os
import logging
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("TradeRestrictionTest")

def test_trade_restrictions():
    """Test that only allowed models can generate trade signals."""
    try:
        print("🧪 TESTING TRADE OPENING RESTRICTIONS")
        print("=" * 60)
        
        # Import required components
        from synthetic_data_collector import SyntheticDataCollector
        from synthetic_pattern_detector import SyntheticPatternDetector
        from ai_model_manager import AIModelManager
        from trading_signal_generator import TradingSignalGenerator
        
        print("Step 1: Initializing components...")
        data_collector = SyntheticDataCollector()
        pattern_detector = SyntheticPatternDetector(data_collector)
        ai_manager = AIModelManager(data_collector, pattern_detector)
        signal_generator = TradingSignalGenerator(ai_manager, pattern_detector, data_collector)
        
        print("✅ Components initialized")
        
        # Test 1: Check trade opening models configuration
        print("\n🔍 TEST 1: Trade Opening Models Configuration")
        print("-" * 40)
        
        trade_opening_models = ai_manager.get_trade_opening_models()
        expected_models = {"short_term_pattern_nn", "medium_term_volatility_xgb"}
        
        print(f"Expected trade-opening models: {sorted(expected_models)}")
        print(f"Actual trade-opening models: {sorted(trade_opening_models)}")
        
        if trade_opening_models == expected_models:
            print("✅ Trade opening models configuration is correct")
        else:
            print("❌ Trade opening models configuration is incorrect")
            return False
        
        # Test 2: Check individual model permissions
        print("\n🔍 TEST 2: Individual Model Permissions")
        print("-" * 40)
        
        all_models = [
            "short_term_pattern_nn",      # Should be allowed
            "short_term_momentum_rf",     # Should NOT be allowed
            "short_term_reversion_gb",    # Should NOT be allowed
            "medium_term_trend_lstm",     # Should NOT be allowed
            "medium_term_breakout_rf",    # Should NOT be allowed
            "medium_term_volatility_xgb", # Should be allowed
            "long_term_macro_dnn",        # Should NOT be allowed
            "long_term_levels_rf",        # Should NOT be allowed
            "long_term_portfolio_gb"      # Should NOT be allowed
        ]
        
        for model_name in all_models:
            is_allowed = ai_manager.is_trade_opening_model(model_name)
            should_be_allowed = model_name in expected_models
            
            status = "✅ ALLOWED" if is_allowed else "❌ BLOCKED"
            expected = "SHOULD BE ALLOWED" if should_be_allowed else "SHOULD BE BLOCKED"
            
            print(f"   {model_name:<25} {status:<12} ({expected})")
            
            if is_allowed != should_be_allowed:
                print(f"❌ ERROR: {model_name} permission is incorrect!")
                return False
        
        print("✅ All model permissions are correct")
        
        # Test 3: Check model status includes trade restrictions
        print("\n🔍 TEST 3: Model Status Information")
        print("-" * 40)
        
        model_status = ai_manager.get_model_status()
        
        if "trade_opening_models" in model_status:
            status_trade_models = set(model_status["trade_opening_models"])
            print(f"Trade opening models in status: {sorted(status_trade_models)}")
            
            if status_trade_models == expected_models:
                print("✅ Model status includes correct trade opening models")
            else:
                print("❌ Model status has incorrect trade opening models")
                return False
        else:
            print("❌ Model status missing trade_opening_models field")
            return False
        
        # Test 4: Verify all models are still configured (just restricted)
        print("\n🔍 TEST 4: All Models Still Configured")
        print("-" * 40)
        
        configured_models = set(ai_manager.model_configs.keys())
        expected_all_models = set(all_models)
        
        print(f"Expected total models: {len(expected_all_models)}")
        print(f"Actual configured models: {len(configured_models)}")
        
        if configured_models == expected_all_models:
            print("✅ All 9 models are still configured")
        else:
            missing = expected_all_models - configured_models
            extra = configured_models - expected_all_models
            if missing:
                print(f"❌ Missing models: {missing}")
            if extra:
                print(f"❌ Extra models: {extra}")
            return False
        
        print("\n🎉 ALL TESTS PASSED!")
        print("=" * 60)
        print("✅ Trade restrictions are working correctly")
        print(f"✅ Only {len(expected_models)} models can open trades: {', '.join(sorted(expected_models))}")
        print(f"✅ {len(all_models) - len(expected_models)} models participate in consensus but cannot open trades")
        print("✅ System functionality preserved")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_trade_restrictions()
    sys.exit(0 if success else 1)
