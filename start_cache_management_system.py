#!/usr/bin/env python3
"""
Integrated Cache Management System Startup
Combines daily cleanup scheduler and memory monitoring for optimal AI trading system performance.
"""

import os
import sys
import time
import logging
from datetime import datetime, timedelta
from threading import Thread
import signal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/cache_management.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('CacheManagement')

class IntegratedCacheManagementSystem:
    """Integrated system combining daily cleanup and memory monitoring."""
    
    def __init__(self):
        self.memory_monitor = None
        self.cleanup_scheduler = None
        self.running = False
        
        # System configuration
        self.config = {
            'memory_threshold': 80,  # 80% RAM threshold
            'memory_check_interval': 300,  # 5 minutes
            'daily_cleanup_time': "06:00",  # 6 AM GMT comprehensive cleanup and P&L reset
            'light_cleanup_times': ["02:00", "10:00", "14:00", "18:00", "22:00"],  # 4-hour light cleanup
            'emergency_cleanup_enabled': True,
            'hybrid_mode': True  # Enable hybrid cleanup approach
        }
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown (only works in main thread)."""
        try:
            def signal_handler(signum, frame):
                logger.info(f"🛑 Received signal {signum}, shutting down gracefully...")
                self.shutdown()
                sys.exit(0)

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            logger.debug("✅ Signal handlers set up successfully")
        except ValueError as e:
            # This happens when not in main thread - it's expected when running as daemon thread
            logger.debug(f"Signal handlers not set up (running in thread): {e}")
        except Exception as e:
            logger.warning(f"Failed to set up signal handlers: {e}")
    
    def start_memory_monitoring(self):
        """Start memory monitoring system."""
        try:
            from memory_monitor_system import MemoryMonitorSystem
            
            logger.info("🧠 Starting memory monitoring system...")
            
            self.memory_monitor = MemoryMonitorSystem(
                threshold_percent=self.config['memory_threshold'],
                check_interval=self.config['memory_check_interval']
            )
            
            self.memory_monitor.start_monitoring()
            logger.info("✅ Memory monitoring system started")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start memory monitoring: {e}")
            return False
    
    def start_daily_cleanup_scheduler(self):
        """Start hybrid cleanup scheduler (daily full + 4-hour light)."""
        try:
            import schedule
            from daily_cache_cleanup_system import DailyCacheCleanupSystem

            logger.info("⏰ Starting hybrid cleanup scheduler...")

            def run_full_cleanup():
                """Run comprehensive daily cleanup and P/L reset."""
                logger.info("🧹 Running comprehensive daily cleanup and P/L reset...")
                
                # Run cache cleanup
                cleanup_system = DailyCacheCleanupSystem()
                cleanup_success = cleanup_system.run_daily_cleanup()
                
                # Run daily P/L reset
                pnl_reset_success = self._run_daily_pnl_reset()
                
                overall_success = cleanup_success and pnl_reset_success
                
                if overall_success:
                    logger.info("✅ Comprehensive daily cleanup and P/L reset completed successfully")
                else:
                    if not cleanup_success:
                        logger.error("❌ Daily cleanup failed")
                    if not pnl_reset_success:
                        logger.error("❌ Daily P/L reset failed")
                    logger.error("❌ Some daily maintenance tasks failed")

            def run_light_cleanup():
                """Run light cleanup (memory + temp files only)."""
                logger.info("🧽 Running light cleanup...")
                cleanup_system = DailyCacheCleanupSystem()
                success = cleanup_system.run_light_cleanup()

                if success:
                    logger.info("✅ Light cleanup completed successfully")
                else:
                    logger.error("❌ Light cleanup failed")

            # Schedule comprehensive daily cleanup at midnight
            schedule.every().day.at(self.config['daily_cleanup_time']).do(run_full_cleanup)

            # Schedule light cleanup every 4 hours
            for cleanup_time in self.config['light_cleanup_times']:
                schedule.every().day.at(cleanup_time).do(run_light_cleanup)

            # Start scheduler in separate thread
            def scheduler_loop():
                logger.info(f"⏰ Comprehensive cleanup scheduled for {self.config['daily_cleanup_time']}")
                logger.info(f"⏰ Light cleanup scheduled for {', '.join(self.config['light_cleanup_times'])}")
                while self.running:
                    schedule.run_pending()
                    time.sleep(60)  # Check every minute
                logger.info("⏰ Hybrid cleanup scheduler stopped")

            self.cleanup_scheduler = Thread(target=scheduler_loop, daemon=True)
            self.cleanup_scheduler.start()

            logger.info("✅ Hybrid cleanup scheduler started")
            logger.info(f"   📅 Full cleanup: {self.config['daily_cleanup_time']} daily")
            logger.info(f"   🧽 Light cleanup: Every 4 hours ({len(self.config['light_cleanup_times'])} times/day)")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to start hybrid cleanup scheduler: {e}")
            return False
    
    def _run_daily_pnl_reset(self):
        """Run daily P/L reset by directly manipulating shared counters file."""
        try:
            logger.info("💰 Running daily P/L reset...")
            
            import json
            import os
            from datetime import datetime
            
            # Path to shared counters file
            shared_counters_file = "shared_counters.json"
            
            # Reset counters directly in the shared file
            current_date = datetime.now().date().isoformat()
            
            reset_data = {
                "daily_trade_count": 0,
                "daily_pnl": 0.0,
                "last_reset_date": current_date,
                "fresh_start_time": None,
                "timeframe_daily_trades": {
                    "short_term": 0,
                    "medium_term": 0,
                    "long_term": 0
                }
            }
            
            # Save to shared counters file
            try:
                with open(shared_counters_file, 'w') as f:
                    json.dump(reset_data, f, indent=2)
                logger.info(f"✅ Shared counters file updated: {shared_counters_file}")
            except Exception as e:
                logger.warning(f"⚠️  Could not update shared counters file: {e}")
            
            # Log the reset details
            logger.info(f"🔄 Daily P/L counters reset:")
            logger.info(f"   📊 Daily trade count: {reset_data['daily_trade_count']}")
            logger.info(f"   💰 Daily P&L: {reset_data['daily_pnl']}")
            logger.info(f"   📅 Last reset date: {reset_data['last_reset_date']}")
            logger.info(f"   ⏰ Timeframe counters: {reset_data['timeframe_daily_trades']}")
            
            logger.info("✅ Daily P/L reset completed successfully (file-based reset)")
            logger.info("ℹ️  Note: Running trading engine will pick up reset values on next signal")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error during daily P/L reset: {e}")
            return False
    
    def run_initial_system_check(self):
        """Run initial system health check."""
        logger.info("🔍 Running initial system health check...")
        
        try:
            from memory_monitor_system import MemoryMonitorSystem
            from daily_cache_cleanup_system import DailyCacheCleanupSystem
            
            # Memory check
            temp_monitor = MemoryMonitorSystem()
            memory_info = temp_monitor.get_memory_info()
            
            if memory_info:
                logger.info(f"📊 Initial memory usage: {memory_info['system_percent']:.1f}%")
                logger.info(f"📊 Available memory: {memory_info['system_available_gb']:.2f} GB")
                
                if memory_info['system_percent'] >= self.config['memory_threshold']:
                    logger.warning("⚠️  High memory usage detected - consider manual cleanup")
                else:
                    logger.info("✅ Memory usage within normal range")
            
            # Check last cleanup
            cleanup_reports_dir = "logs/cleanup_reports"
            if os.path.exists(cleanup_reports_dir):
                reports = [f for f in os.listdir(cleanup_reports_dir) if f.startswith('cleanup_report_')]
                if reports:
                    latest_report = max(reports)
                    report_date = latest_report.replace('cleanup_report_', '').replace('.txt', '')
                    logger.info(f"📋 Last cleanup report: {report_date}")
                else:
                    logger.info("📋 No previous cleanup reports found")
            
            logger.info("✅ Initial system health check completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error during initial system check: {e}")
            return False
    
    def start_integrated_system(self):
        """Start the complete integrated cache management system."""
        logger.info("🚀 STARTING INTEGRATED CACHE MANAGEMENT SYSTEM")
        logger.info("="*80)
        logger.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Run initial system check
        if not self.run_initial_system_check():
            logger.error("❌ Initial system check failed")
            return False
        
        self.running = True
        
        # Start memory monitoring
        if not self.start_memory_monitoring():
            logger.error("❌ Failed to start memory monitoring")
            return False
        
        # Start daily cleanup scheduler
        if not self.start_daily_cleanup_scheduler():
            logger.error("❌ Failed to start daily cleanup scheduler")
            return False
        
        logger.info("🎉 INTEGRATED CACHE MANAGEMENT SYSTEM STARTED SUCCESSFULLY")
        logger.info("="*80)
        logger.info("📊 System Status:")
        logger.info(f"   🧠 Memory monitoring: ACTIVE (threshold: {self.config['memory_threshold']}%)")
        logger.info(f"   ⏰ Daily cleanup: SCHEDULED ({self.config['daily_cleanup_time']})")
        logger.info(f"   🛡️ AI model protection: ENABLED")
        logger.info(f"   🚨 Emergency cleanup: {'ENABLED' if self.config['emergency_cleanup_enabled'] else 'DISABLED'}")
        
        return True
    
    def shutdown(self):
        """Shutdown the integrated system gracefully."""
        logger.info("🛑 Shutting down integrated cache management system...")
        
        self.running = False
        
        # Stop memory monitoring
        if self.memory_monitor:
            self.memory_monitor.stop_monitoring()
            logger.info("✅ Memory monitoring stopped")
        
        # Cleanup scheduler will stop when self.running = False
        if self.cleanup_scheduler and self.cleanup_scheduler.is_alive():
            self.cleanup_scheduler.join(timeout=5)
            logger.info("✅ Daily cleanup scheduler stopped")
        
        logger.info("✅ Integrated cache management system shutdown complete")
    
    def get_system_status(self) -> dict:
        """Get current system status."""
        status = {
            'running': self.running,
            'start_time': datetime.now().isoformat(),
            'memory_monitoring': False,
            'daily_cleanup_scheduled': False,
            'config': self.config
        }
        
        if self.memory_monitor:
            status['memory_monitoring'] = self.memory_monitor.monitoring
            status['memory_stats'] = self.memory_monitor.get_monitoring_stats()
        
        if self.cleanup_scheduler:
            status['daily_cleanup_scheduled'] = self.cleanup_scheduler.is_alive()
        
        return status

def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Integrated Cache Management System')
    parser.add_argument('--test-cleanup', action='store_true', help='Run test cleanup and exit')
    parser.add_argument('--test-memory', action='store_true', help='Run memory test and exit')
    parser.add_argument('--test-pnl-reset', action='store_true', help='Run test daily P/L reset and exit')
    parser.add_argument('--status', action='store_true', help='Show system status and exit')
    
    args = parser.parse_args()
    
    if args.test_cleanup:
        # Test cleanup
        logger.info("🧪 Running test cleanup...")
        from daily_cache_cleanup_system import DailyCacheCleanupSystem
        
        cleanup_system = DailyCacheCleanupSystem()
        success = cleanup_system.run_daily_cleanup()
        
        if success:
            logger.info("✅ Test cleanup completed successfully")
        else:
            logger.error("❌ Test cleanup failed")
        
        return success
    
    elif args.test_memory:
        # Test memory monitoring
        logger.info("🧪 Running memory monitoring test...")
        from memory_monitor_system import MemoryMonitorSystem
        
        monitor = MemoryMonitorSystem()
        memory_info = monitor.get_memory_info()
        
        if memory_info:
            logger.info(f"📊 Memory usage: {memory_info['system_percent']:.1f}%")
            logger.info(f"📊 Available: {memory_info['system_available_gb']:.2f} GB")
            logger.info("✅ Memory monitoring test completed")
            return True
        else:
            logger.error("❌ Memory monitoring test failed")
            return False
    
    elif args.test_pnl_reset:
        # Test daily P/L reset
        logger.info("🧪 Running daily P/L reset test...")
        
        system = IntegratedCacheManagementSystem()
        success = system._run_daily_pnl_reset()
        
        if success:
            logger.info("✅ Daily P/L reset test completed successfully")
        else:
            logger.error("❌ Daily P/L reset test failed")
        
        return success
    
    elif args.status:
        # Show status
        system = IntegratedCacheManagementSystem()
        status = system.get_system_status()
        
        print("\n🔍 CACHE MANAGEMENT SYSTEM STATUS")
        print("="*50)
        print(f"Configuration: {status['config']}")
        print(f"Running: {status['running']}")
        print(f"Memory monitoring: {status['memory_monitoring']}")
        print(f"Daily cleanup scheduled: {status['daily_cleanup_scheduled']}")
        
        return True
    
    else:
        # Start integrated system
        system = IntegratedCacheManagementSystem()
        
        if not system.start_integrated_system():
            logger.error("❌ Failed to start integrated system")
            return False
        
        try:
            # Keep running
            logger.info("🔄 System running... Press Ctrl+C to stop")
            while system.running:
                time.sleep(60)
                
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown requested by user")
        except Exception as e:
            logger.error(f"❌ Unexpected error: {e}")
        finally:
            system.shutdown()
        
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
