{"timestamp": "2025-07-08T14:50:41.466644", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8728154301643372, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T14:50:41.531921", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.46486776075335934, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T14:50:41.537518", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T14:50:41.791596", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.4765327572822571, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T14:50:41.862138", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.35349925768506085, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T14:50:41.876296", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999129772186279, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T14:50:41.998070", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999287128448486, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T14:50:42.065794", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.55, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T14:50:42.071490", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T14:54:15.563360", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.50045245885849, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T14:54:15.629202", "model_name": "short_term_momentum_rf", "signal": -1, "confidence": 0.5166458600467483, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T14:54:15.635967", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T14:54:15.850713", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8405032157897949, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T14:54:15.913395", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.3952894806104393, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T14:54:15.923196", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997953772544861, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T14:54:16.040780", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999860525131226, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T14:54:16.105795", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.555, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T14:54:16.110670", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T14:57:13.826553", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5337101817131042, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T14:57:13.929361", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9050265906276436, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T14:57:13.934412", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T14:57:13.995314", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8407726883888245, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T14:57:14.059675", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6555233622630972, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T14:57:14.069391", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999079704284668, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T14:57:14.149378", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999595880508423, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T14:57:14.218106", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T14:57:14.224692", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:00:13.747413", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5337101817131042, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:00:13.960807", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9050265906276436, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:00:13.966886", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:00:14.026798", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8407726883888245, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:00:14.089115", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6555233622630972, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:00:14.107937", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999079704284668, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:00:14.189082", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999595880508423, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:00:14.256658", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:00:14.275561", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:03:13.745421", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6413944959640503, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:03:13.809219", "model_name": "short_term_momentum_rf", "signal": 1, "confidence": 0.7692181638551219, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:03:13.814786", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:03:13.880970", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8269127607345581, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:03:13.945416", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.6463059989347516, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:03:13.954124", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9982170462608337, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:03:14.037475", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999977350234985, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:03:14.104445", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:03:14.110328", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:06:13.741205", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6994701623916626, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:06:13.807678", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8893209366156927, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:06:13.813165", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:06:13.883195", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8473442196846008, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:06:13.946397", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3577925498115663, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:06:13.956498", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9996377229690552, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:06:14.039723", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999737739562988, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:06:14.106456", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:06:14.111273", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:09:14.123629", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.758400559425354, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:09:14.189400", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9554188821447147, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:09:14.194851", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:09:14.258840", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8344598412513733, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:09:14.326115", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.39663297600438485, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:09:14.337677", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9985427856445312, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:09:14.420784", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999973773956299, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:09:14.490690", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:09:14.496026", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:12:13.917011", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8508270978927612, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:12:13.997014", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9754965999343522, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:12:14.002290", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:12:14.067026", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.830762505531311, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:12:14.131066", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.335960762535004, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:12:14.140548", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9991820454597473, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:12:14.220713", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999977350234985, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:12:14.287715", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:12:14.292160", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:15:13.806562", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9930612444877625, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:15:14.217169", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9900093844406425, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:15:15.245705", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:15:15.321388", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8509066700935364, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:15:15.386243", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3605779671399234, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:15:15.396927", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9996392726898193, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:15:15.488159", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999995231628418, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:15:15.554673", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:15:15.560060", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:18:13.658705", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7475693225860596, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:18:13.733283", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9633092219378661, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:18:13.738541", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:18:13.802960", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8360638618469238, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:18:13.867971", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4867809603088695, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:18:13.878012", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9987820982933044, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:18:13.956343", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999971389770508, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:18:14.023001", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:18:14.028415", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:21:13.581738", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9986218214035034, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:21:14.026891", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.6531780127860844, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:21:14.031973", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:21:14.088227", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6958560943603516, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:21:14.149492", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.6506220013009519, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:21:14.172866", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.5640153884887695, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:21:14.246272", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999947547912598, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:21:14.308269", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:21:14.323034", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:24:13.727731", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9948467016220093, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:24:13.802673", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.995, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:24:13.807927", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:24:13.864254", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8169018030166626, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:24:13.927624", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4969078171601051, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:24:13.937380", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8094443082809448, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:24:14.013721", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999861717224121, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:24:14.082671", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:24:14.087801", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:27:13.907452", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9710139632225037, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:27:13.970946", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9990000000000001, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:27:13.977060", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:27:14.036114", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8573351502418518, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:27:14.100805", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.402017718396221, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:27:14.109926", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9976305961608887, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:27:14.185772", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999994039535522, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:27:14.251467", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:27:14.257057", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:30:13.656770", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9633519649505615, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:30:13.724679", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:30:13.729756", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:30:13.787884", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.83279949426651, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:30:13.849336", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4378362152967151, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:30:13.858909", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997809529304504, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:30:13.930603", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999997615814209, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:30:13.996662", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:30:14.002186", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:33:13.670994", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9836146235466003, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:33:13.737002", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9957981601731603, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:33:13.742233", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:33:13.799773", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8473544120788574, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:33:13.860669", "model_name": "medium_term_breakout_rf", "signal": -2, "confidence": 0.5536978066541036, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:33:13.870659", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999613761901855, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:33:13.943196", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999440908432007, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:33:14.007542", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:33:14.013492", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:36:13.610318", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9979836940765381, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:36:13.672772", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9918683964340745, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:36:13.686417", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:36:13.745363", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5771427154541016, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:36:13.808043", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8759231025330522, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:36:13.816902", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999309778213501, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:36:13.890387", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999996542930603, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:36:13.956659", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:36:13.960677", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:39:13.546825", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9973554611206055, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:39:14.674279", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:39:14.678297", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:39:14.736598", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8390974998474121, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:39:14.796369", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7947950425893267, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:39:14.820141", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999403953552246, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:39:14.891854", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999734163284302, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:39:14.958758", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:39:14.972821", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:42:13.508396", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9978073239326477, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:42:13.588090", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:42:13.592647", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:42:13.649383", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8331769704818726, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:42:13.710780", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6465265216677224, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:42:13.735720", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998136162757874, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:42:13.808165", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999973773956299, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:42:13.870288", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:42:13.880133", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:45:13.541833", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9954055547714233, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:45:13.612471", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99925, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:45:13.618548", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:45:13.676238", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8320053815841675, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:45:13.738047", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6307828630189815, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:45:13.746766", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999968409538269, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:45:13.823644", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999725818634033, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:45:13.889531", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.56, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:45:13.894552", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:48:13.558309", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5279699563980103, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:48:13.654547", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9632900432900433, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:48:13.659575", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:48:13.714737", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8023619055747986, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:48:13.777466", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4819571872048998, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:48:13.802057", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998704195022583, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:48:13.874636", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999521970748901, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:48:13.939834", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:48:13.946754", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:51:13.688258", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5738391876220703, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:51:13.759960", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9728712121212123, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:51:13.765763", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:51:13.824338", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8333482146263123, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:51:13.884847", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.42815181863338964, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:51:13.894496", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999452829360962, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:51:13.968995", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999537467956543, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:51:14.035406", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:51:14.039523", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:54:13.868917", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8534045219421387, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:54:13.932611", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9909821428571429, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:54:13.938295", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:54:13.998377", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8408558368682861, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:54:14.060556", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.3633447687803444, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:54:14.069774", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999790191650391, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:54:14.143992", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999616146087646, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:54:14.208740", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:54:14.214218", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T15:57:13.731711", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8049232363700867, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T15:57:14.219355", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T15:57:14.223916", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T15:57:14.281910", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8417338728904724, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T15:57:14.345304", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.551724350317975, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T15:57:14.354253", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999659061431885, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T15:57:14.430552", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999616146087646, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T15:57:14.494699", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T15:57:14.499759", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:00:13.621372", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7926245331764221, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:00:13.690219", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:00:13.694840", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:00:13.753974", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8459312915802002, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:00:13.819093", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8090492893882376, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:00:13.828173", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999688863754272, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:00:14.073689", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:00:14.134277", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:00:14.150533", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:03:13.683601", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.4811412990093231, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:03:14.147180", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9488041900093371, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:03:14.152749", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:03:14.210135", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8462496399879456, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:03:14.274206", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.49333716164932084, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:03:14.294787", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999980092048645, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:03:14.368457", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999576807022095, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:03:14.436303", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:03:14.450240", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:06:13.706309", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5246444940567017, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:06:14.386998", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8084781300282186, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:06:14.391846", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:06:14.446953", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8455938696861267, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:06:14.508523", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.531256801301142, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:06:14.530958", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999275207519531, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:06:14.605361", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999748468399048, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:06:14.672085", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:06:14.682369", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:09:13.612229", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9391511678695679, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:09:14.589564", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:09:14.595388", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:09:14.650542", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7806857824325562, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:09:14.711327", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.3670289741732674, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:09:14.733200", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.9870582222938538, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:09:14.805306", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999827146530151, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:09:14.871693", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:09:14.883367", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:12:13.772040", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9428409934043884, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:12:13.838639", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:12:13.844037", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:12:13.902968", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8166391253471375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:12:13.964501", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.37138420064614097, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:12:13.974519", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.8924287557601929, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:12:14.051766", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999983549118042, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:12:14.118690", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:12:14.123988", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:15:13.613426", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9431362152099609, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:15:13.864434", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:15:13.869548", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:15:13.927869", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8225857019424438, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:15:13.989362", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.31624347506271944, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:15:13.999359", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.6597474217414856, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:15:14.072586", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999850988388062, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:15:14.140088", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:15:14.146334", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:18:13.645600", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.31995731592178345, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:18:13.707212", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9964892595830095, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:18:13.712774", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:18:13.771672", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7902270555496216, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:18:13.833930", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.3511545006869133, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:18:13.842595", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.5671307444572449, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:18:13.916875", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999850988388062, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:18:13.983940", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:18:13.989107", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:21:13.614486", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.33389827609062195, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:21:13.912879", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9040164646789467, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:21:13.917972", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:21:13.975423", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7637227177619934, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:21:14.037520", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.3459448772016379, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:21:14.046628", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.722480058670044, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:21:14.120254", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999872446060181, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:21:14.190207", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:21:14.195425", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:24:13.832681", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.322245717048645, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:24:13.898751", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:24:14.403088", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:24:14.472270", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7995429039001465, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:24:14.533723", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5347907308475991, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:24:14.543316", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.85758376121521, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:24:14.643487", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999948740005493, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:24:14.706197", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:24:14.744642", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:27:13.665433", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.31328144669532776, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:27:13.737050", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:27:13.742138", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:27:13.799647", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8042712807655334, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:27:13.875999", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5399182179971936, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:27:13.885067", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9433214664459229, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:27:13.959039", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999957084655762, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:27:14.048013", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:27:14.052206", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:30:13.652135", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.3008182942867279, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:30:13.725084", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:30:13.730635", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:30:13.788586", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8043347597122192, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:30:13.849663", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5899467791924597, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:30:13.858855", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9675883650779724, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:30:13.933872", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999959468841553, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:30:13.999868", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:30:14.004919", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:33:13.865311", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.54019695520401, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:33:13.930773", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9636340571193511, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:33:13.936834", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:33:13.996748", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5425675511360168, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:33:14.061286", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7038369959044616, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:33:14.070492", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998985528945923, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:33:14.145981", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9998407363891602, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:33:14.211645", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:33:14.216706", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:36:13.683225", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7290080189704895, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:36:13.757873", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9748622007837127, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:36:13.762079", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:36:13.826376", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8039505481719971, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:36:13.887882", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8011339476279641, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:36:13.897722", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999488592147827, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:36:13.974443", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999434947967529, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:36:14.040345", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:36:14.045385", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:39:13.796337", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7248849272727966, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:39:13.860438", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9805111416361416, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:39:13.865462", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:39:13.923295", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8117523193359375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:39:13.986765", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7258555617032003, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:39:13.995276", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999792575836182, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:39:14.071106", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999620914459229, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:39:14.135871", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:39:14.141337", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:42:13.596215", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8010016083717346, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:42:13.674614", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:42:13.680142", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:42:13.738618", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.823885440826416, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:42:13.801283", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9220129994393074, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:42:13.809914", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999687671661377, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:42:13.883727", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999634027481079, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:42:13.959136", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:42:13.965228", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:45:13.683837", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8284468650817871, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:45:13.763216", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:45:13.768408", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:45:13.825946", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8229933381080627, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:45:13.888682", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9268182982363738, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:45:13.897299", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999679327011108, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:45:13.971590", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:45:14.039021", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:45:14.045072", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:48:13.613993", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7470113039016724, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:48:14.145462", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9006626984126985, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:48:14.150494", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:48:14.207029", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7739590406417847, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:48:14.268379", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4043130303534017, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:48:14.278376", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:48:14.351793", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999955415725708, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:48:14.420750", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:48:14.425297", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:51:13.688038", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7693203091621399, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:51:13.752169", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9521194083694086, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:51:13.757758", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:51:13.815718", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8219377398490906, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:51:13.878523", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4651519108338624, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:51:13.887103", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999673366546631, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:51:13.961558", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999562501907349, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:51:14.026985", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:51:14.033074", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:54:13.789033", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7819669842720032, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:54:13.852626", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9971073008849558, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:54:13.857195", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:54:13.918973", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6470022797584534, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:54:13.980668", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4686788984461995, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:54:13.989969", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999656677246094, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:54:14.063532", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999730587005615, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:54:14.131548", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.62, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:54:14.136589", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T16:57:13.776449", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6001560688018799, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T16:57:13.840067", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T16:57:13.845174", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T16:57:13.902914", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7696380615234375, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T16:57:13.965359", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3475192371444853, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T16:57:13.975017", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999203681945801, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T16:57:14.049209", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T16:57:14.113863", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.62, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T16:57:14.119794", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:00:13.598418", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8096263408660889, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:00:13.673546", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9984999999999999, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:00:13.678998", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:00:13.736253", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7968395352363586, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:00:13.805243", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.36125428495082157, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:00:13.813849", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999669790267944, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:00:13.888521", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:00:13.973394", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.62, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:00:13.977913", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:03:13.573361", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9314396381378174, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:03:13.983130", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9900611783904977, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:03:13.989046", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:03:14.044436", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.666284441947937, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:03:14.106121", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4180536278640868, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:03:14.131079", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999476671218872, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:03:14.204107", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999655485153198, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:03:14.269891", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:03:14.287307", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:06:13.605924", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9913823008537292, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:06:13.670767", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9968747474747475, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:06:13.675821", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:06:13.734001", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6559691429138184, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:06:13.797232", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.744025660316222, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:06:13.806042", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999297857284546, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:06:13.885689", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:06:13.953160", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:06:13.957822", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:09:13.761065", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9966172575950623, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:09:13.824906", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:09:13.829971", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:09:13.886758", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7795003056526184, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:09:13.952500", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4285350629674067, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:09:13.961465", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8190663456916809, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:09:14.038163", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999988317489624, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:09:14.102593", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:09:14.108125", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:12:13.837895", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9983002543449402, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:12:13.904227", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:12:13.909994", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:12:13.965513", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.817465603351593, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:12:14.029484", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.45896978066136973, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:12:14.039824", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.7406964898109436, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:12:14.117771", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999912977218628, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:12:14.184238", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:12:14.190424", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:15:13.752578", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9984211921691895, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:15:13.814143", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:15:13.819173", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:15:13.880082", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8099806308746338, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:15:13.941460", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.47213090592740387, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:15:13.952039", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.6259987354278564, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:15:14.031629", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999927282333374, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:15:14.098921", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:15:14.104556", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:18:13.843304", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.671032190322876, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:18:14.232239", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9852775974025972, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:18:14.237450", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:18:14.293427", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.29200366139411926, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:18:14.354618", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.45471607959977106, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:18:14.362651", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999454021453857, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:18:14.436027", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999749660491943, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:18:14.499527", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:18:14.504569", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:21:13.539586", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9981535077095032, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:21:13.620803", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.799152555244364, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:21:13.625745", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:21:13.681397", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5353546142578125, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:21:13.741652", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6037285258724908, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:21:13.763574", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999101161956787, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:21:13.836535", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999998927116394, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:21:13.903354", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:21:13.912369", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:24:13.532335", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9949119687080383, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:24:13.736932", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9917275432900433, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:24:14.343304", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:24:14.408315", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8373145461082458, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:24:14.468122", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6361920922516784, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:24:14.476851", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999351501464844, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:24:14.574542", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999902248382568, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:24:14.633863", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:24:14.638944", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:27:14.737806", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9945764541625977, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:27:14.810556", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9985714285714287, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:27:14.815715", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:27:14.883909", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8392703533172607, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:27:14.952952", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6804861435141356, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:27:14.966740", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999592304229736, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:27:15.055396", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:27:15.135877", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:27:15.142015", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:30:14.052799", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9931768178939819, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:30:14.130335", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:30:14.137628", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:30:14.207686", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8391520977020264, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:30:14.272453", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6585792644470128, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:30:14.282462", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999638795852661, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:30:14.365007", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:30:14.432561", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:30:14.438869", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:33:14.059819", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9257678985595703, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:33:14.350949", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9967798654897015, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:33:14.357313", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:33:14.427985", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8256392478942871, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:33:14.496853", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4912759004674767, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:33:14.507932", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997909665107727, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:33:14.590050", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999953031539917, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:33:14.663744", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:33:14.669467", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:36:14.458164", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9978846907615662, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:36:14.545186", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.816637844816119, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:36:14.707340", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:36:14.783544", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6771619319915771, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:36:14.854116", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4981398272178106, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:36:14.872331", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998766183853149, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:36:14.965656", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999929666519165, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:36:15.039492", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:36:15.049507", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:39:15.449401", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9868371486663818, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:39:16.044736", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9701013484930299, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:39:16.050290", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:39:16.156447", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8368976712226868, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:39:16.253725", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.42182038287759194, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:39:16.268251", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998581409454346, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:39:16.374775", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999940395355225, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:39:16.474016", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:39:16.481276", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:42:15.802827", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9744724631309509, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:42:15.980095", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9994, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:42:15.988308", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:42:16.118067", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8318644762039185, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:42:16.216587", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5657465850349043, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:42:16.236924", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997724890708923, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:42:16.386917", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999740123748779, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:42:16.497436", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:42:16.504706", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:45:15.663917", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9747897982597351, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:45:15.780049", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9994, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:45:15.789097", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:45:15.900500", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8308935165405273, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:45:16.004383", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.6237921296085658, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:45:16.020047", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999397993087769, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:45:16.165115", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:45:16.270896", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:45:16.280516", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:48:15.797382", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9447504878044128, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:48:15.904987", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9736447862343486, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:48:15.912239", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:48:16.024455", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8326807022094727, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:48:16.128087", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6788581438008038, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:48:16.147634", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998874664306641, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:48:16.256102", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:48:16.360850", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:48:16.367722", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:51:16.043282", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9575474858283997, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:51:16.128669", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.975992497810309, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:51:16.135893", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:51:16.238753", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8323460817337036, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:51:16.338142", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6397442908564539, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:51:16.353494", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998469352722168, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:51:16.454313", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999643564224243, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:51:16.547542", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:51:16.556221", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:54:14.405468", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9547678232192993, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:54:15.606070", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9878739316239316, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:54:15.611805", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:54:15.688532", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8485808968544006, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:54:15.768390", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.42980531015411133, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:54:15.780779", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.554980993270874, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:54:15.877678", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999918937683105, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:54:15.952766", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:54:15.959443", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T17:57:15.911367", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9594119787216187, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T17:57:16.020486", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9878739316239316, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T17:57:16.027014", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T17:57:16.141346", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8476276993751526, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T17:57:16.246453", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4424632494091641, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T17:57:16.266648", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.724615752696991, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T17:57:16.398640", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999991774559021, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T17:57:16.523878", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T17:57:16.566298", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:00:16.780634", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9758140444755554, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:00:16.885622", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.991875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:00:16.893652", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:00:17.001448", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8469216227531433, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:00:17.114438", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4670854376931082, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:00:17.136685", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9982611536979675, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:00:17.260066", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999903440475464, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:00:17.518890", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:00:17.528088", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:03:15.653590", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9349746108055115, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:03:15.807376", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9943627450980392, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:03:15.815510", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:03:15.916290", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7962560653686523, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:03:16.017909", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5552123533512997, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:03:16.044656", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999875545501709, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:03:16.180475", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999474287033081, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:03:16.295404", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.625, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:03:16.302716", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:06:15.770692", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9707286357879639, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:06:16.874195", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.706907448653746, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:06:16.882789", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:06:16.994824", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8412582278251648, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:06:17.105565", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5011473665460302, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:06:17.122539", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999634027481079, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:06:17.232161", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999903440475464, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:06:17.342746", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.625, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:06:17.351223", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:09:16.399343", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.728131115436554, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:09:16.512102", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.996232142857143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:09:16.521102", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:09:16.633018", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8364768624305725, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:09:16.741507", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5491067596653199, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:09:16.759822", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999799728393555, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:09:16.870019", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999622106552124, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:09:16.978432", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.625, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:09:16.985590", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:12:15.670325", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8297252058982849, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:12:15.766516", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:12:15.775514", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:12:15.879305", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8455125093460083, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:12:15.981258", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8327534440607629, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:12:16.002224", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999573230743408, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:12:16.111913", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999632835388184, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:12:16.199450", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.625, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:12:16.205933", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:15:16.236126", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8448697924613953, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:15:16.346140", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:15:16.353690", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:15:17.008943", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8460004329681396, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:15:17.113816", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8262221927450287, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:15:17.134381", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999657869338989, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:15:17.279352", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:15:17.382191", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.625, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:15:17.389774", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:18:15.060509", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7450411319732666, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:18:15.149206", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9385897516698988, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:18:15.155629", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:18:15.257215", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8462571501731873, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:18:15.352974", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.45236537036115065, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:18:15.368068", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999977707862854, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:18:15.481943", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999579191207886, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:18:15.582293", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:18:15.591538", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:21:16.475669", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7336270213127136, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:21:16.617024", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.973924963924964, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:21:16.631162", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:21:16.739733", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8456817865371704, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:21:16.898309", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5255597420262518, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:21:16.917029", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999399185180664, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:21:17.056494", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999743700027466, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:21:17.171392", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:21:17.178923", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:24:15.189768", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4796559512615204, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:24:15.266268", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:24:15.272294", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:24:15.358621", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7822109460830688, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:24:15.460704", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.6472631940402033, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:24:15.476655", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9967879056930542, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:24:15.588067", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999981164932251, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:24:15.661286", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:24:15.668298", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:27:15.743398", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6723072528839111, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:27:15.864490", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:27:15.872348", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:27:16.001555", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.827642023563385, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:27:16.124892", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.6757576786665679, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:27:16.142792", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9983742237091064, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:27:16.259614", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999978542327881, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:27:16.370922", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:27:16.378993", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:30:15.498524", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8271934986114502, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:30:15.673424", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9989285714285714, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:30:15.680410", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:30:15.787288", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7901396751403809, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:30:15.890043", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.6202907765557815, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:30:15.927549", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9995050430297852, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:30:16.050181", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999982118606567, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:30:16.150235", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:30:16.156869", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:33:15.931010", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9808449745178223, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:33:16.043546", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9842494526546253, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:33:16.052460", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:33:16.166086", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6975414752960205, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:33:16.286556", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.34307465949351773, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:33:16.305671", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9990984201431274, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:33:16.435438", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999960660934448, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:33:16.540953", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:33:16.549533", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:36:16.102840", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9848317503929138, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:36:17.295001", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9871710796387522, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:36:17.304061", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:36:17.412189", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7282263040542603, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:36:17.520299", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.4115467572780968, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:36:17.534052", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9950647950172424, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:36:17.644996", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999996542930603, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:36:17.725793", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:36:17.733046", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:39:14.960747", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9914030432701111, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:39:15.046476", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9817088328338328, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:39:15.897807", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:39:16.001151", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7886541485786438, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:39:16.089658", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.6140767215355837, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:39:16.103942", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9932832717895508, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:39:16.198204", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999988079071045, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:39:16.274878", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:39:16.281119", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:42:16.087519", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9927962422370911, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:42:16.211109", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:42:16.218013", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:42:16.338848", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7871620655059814, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:42:16.445253", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5482412432935052, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:42:16.463321", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9991550445556641, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:42:16.616201", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999988079071045, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:42:16.710641", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:42:16.718451", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:45:16.162147", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9926129579544067, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:45:16.941815", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:45:16.950245", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:45:17.070610", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7681058645248413, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:45:17.191508", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.5463779690013794, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:45:17.213738", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9985505938529968, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:45:17.334075", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999998927116394, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:45:17.449513", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.62, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:45:17.458462", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:48:16.477721", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.48536568880081177, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:48:16.591074", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9757857142857143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:48:16.624232", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:48:16.756731", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5125338435173035, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:48:16.879244", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.927963430784907, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:48:16.895975", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999591112136841, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:48:17.023073", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999483823776245, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:48:17.130918", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:48:17.139475", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:51:15.369394", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9975789189338684, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:51:15.466780", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9061965519860258, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:51:15.498056", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:51:15.608437", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5535463094711304, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:51:15.700815", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8801721639132826, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:51:15.725745", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999066591262817, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:51:15.845655", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999966621398926, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:51:15.954725", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:51:15.962291", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:54:16.088239", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9707993268966675, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:54:16.190367", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9825997251273801, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:54:16.197767", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:54:16.303801", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8394912481307983, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:54:16.401648", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8777426518653489, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:54:16.416094", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999533891677856, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:54:16.534038", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:54:16.615820", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:54:16.623298", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T18:57:13.726815", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9973268508911133, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T18:57:14.476232", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T18:57:14.481740", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T18:57:14.539875", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.839352011680603, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T18:57:14.601233", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.770652584039704, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T18:57:14.625848", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999704360961914, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T18:57:14.702930", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T18:57:14.767680", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T18:57:14.781654", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:00:13.671656", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9975395202636719, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:00:13.988823", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9953333333333333, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:00:13.993978", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:00:14.051546", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8392592072486877, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:00:14.112627", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7617294672812985, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:00:14.122437", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999700784683228, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:00:14.197596", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:00:14.265198", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:00:14.270774", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:03:13.790255", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9140257835388184, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:03:13.865686", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9551651523541058, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:03:13.872042", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:03:13.930666", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7771386504173279, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:03:13.993447", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8622871914531065, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:03:14.002997", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998760223388672, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:03:14.077437", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999516010284424, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:03:14.144982", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:03:14.150582", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:06:13.737104", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9547735452651978, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:06:13.800658", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.4637190184165547, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:06:13.805472", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:06:13.866212", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8122419118881226, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:06:13.929887", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8406942363034184, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:06:13.939319", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999666213989258, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:06:14.014987", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999901056289673, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:06:14.082396", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:06:14.087973", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:09:13.655603", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6531016230583191, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:09:14.640505", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9922492368742369, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:09:14.645562", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:09:14.706726", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8109868764877319, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:09:14.769202", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8336385432497658, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:09:14.779105", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999778270721436, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:09:14.855210", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999614953994751, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:09:14.923215", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:09:14.928738", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:12:13.661955", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9483625292778015, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:12:14.153545", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9984615384615384, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:12:14.158580", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:12:14.216552", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.813129186630249, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:12:14.277577", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8258515566756409, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:12:14.287701", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999594688415527, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:12:14.363772", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999613761901855, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:12:14.426267", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:12:14.432365", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:15:15.005084", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8267403841018677, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:15:15.264441", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:15:15.271986", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:15:15.349269", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8231362104415894, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:15:15.415963", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9148119166390174, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:15:15.427541", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999641180038452, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:15:15.513213", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:15:15.585128", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:15:15.590827", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:18:14.766964", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6591123938560486, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:18:14.851065", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9163326118326119, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:18:14.857596", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:18:14.945113", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8111687302589417, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:18:15.025748", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3917843714468237, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:18:15.038273", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999613761901855, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:18:15.137070", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999566078186035, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:18:15.209355", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:18:15.215344", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:21:15.633854", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5346036553382874, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:21:15.759117", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.6149050346369317, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:21:15.768263", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:21:15.878213", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.766132652759552, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:21:15.986558", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.3739861252850894, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:21:16.005340", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999570846557617, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:21:16.197405", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999889135360718, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:21:16.320767", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:21:16.329018", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:24:16.189010", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6819861531257629, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:24:16.285661", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.998125, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:24:16.293470", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:24:16.390839", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6555730104446411, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:24:16.513134", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4581217624257679, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:24:16.530610", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999635219573975, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:24:16.679594", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999730587005615, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:24:16.772654", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:24:16.779678", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:27:15.523785", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5077859163284302, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:27:15.619900", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:27:15.627226", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:27:15.720632", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7951706051826477, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:27:15.843415", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4984294145082767, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:27:15.860624", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999968409538269, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:27:15.986022", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:27:16.060246", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.62, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:27:16.067805", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:30:15.795478", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.4268733561038971, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:30:15.898337", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.999875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:30:15.905843", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:30:16.016612", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7550873756408691, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:30:16.120877", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.38476832507849223, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:30:16.139419", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999523162841797, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:30:16.288245", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999725818634033, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:30:16.377734", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.62, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:30:16.394198", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:33:15.175567", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.3582395911216736, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:33:15.277691", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9742003968253968, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:33:15.284311", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:33:15.383242", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.39523616433143616, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:33:15.480827", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5227908140324115, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:33:15.497318", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999486207962036, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:33:15.619446", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999493360519409, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:33:15.718447", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.7, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:33:15.726402", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:36:15.722735", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.997215747833252, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:36:15.836232", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9305890869094445, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:36:15.843065", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:36:15.952800", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.4899430572986603, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:36:16.060879", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.869737981899154, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:36:16.091716", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998711347579956, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:36:16.225555", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999958276748657, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:36:16.337614", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:36:16.344176", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:39:16.508060", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9279969334602356, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:39:16.612546", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9975277554304103, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:39:16.620195", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:39:16.740963", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8426638841629028, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:39:16.850178", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8349325739226252, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:39:16.870080", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998235106468201, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:39:16.995186", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999736547470093, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:39:17.100321", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:39:17.107229", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:42:16.657442", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9970400929450989, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:42:16.767151", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9995454545454545, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:42:16.775207", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:42:16.893265", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8414082527160645, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:42:16.995744", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.6161955589397605, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:42:17.021863", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999592304229736, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:42:17.176743", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:42:17.293545", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:42:17.302718", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:45:15.509911", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9970782995223999, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:45:15.627524", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9995454545454545, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:45:15.634937", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:45:15.747298", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.839259922504425, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:45:15.856968", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.5874643158906117, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:45:15.871564", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999592304229736, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:45:16.014088", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:45:16.119539", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:45:16.126623", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:48:15.245453", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9189496636390686, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:48:15.329981", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.939129728569602, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:48:15.336323", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:48:15.418216", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8404425382614136, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:48:15.505048", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5662958422951722, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:48:15.517363", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9996644258499146, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:48:15.615585", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999557733535767, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:48:15.689699", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:48:15.696126", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:51:15.145003", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.927971601486206, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:51:15.397390", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.6206369109020495, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:51:15.405143", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:51:15.512462", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8417552709579468, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:51:15.598773", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3385880816002434, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:51:15.611462", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997504353523254, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:51:15.748798", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999924898147583, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:51:15.843389", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:51:15.849392", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:54:15.601647", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9453132748603821, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:54:15.706253", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9755761746423508, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:54:15.714900", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:54:15.815584", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8345394730567932, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:54:15.935634", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3621782890274463, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:54:15.961303", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999778687953949, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:54:16.114992", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999921321868896, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:54:16.223795", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:54:16.230944", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T19:57:15.615360", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9556707143783569, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T19:57:16.547209", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.995625, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T19:57:16.554974", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T19:57:16.650499", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8339741230010986, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T19:57:16.745287", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.37734043941430223, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T19:57:16.764095", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999969482421875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T19:57:16.923592", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999905824661255, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T19:57:17.011859", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T19:57:17.021013", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:00:16.183602", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9627024531364441, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:00:16.301142", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:00:16.308260", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:00:16.419842", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8367673754692078, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:00:16.523764", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5406138592499511, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:00:16.538060", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999732971191406, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:00:16.694500", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:00:16.791514", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:00:16.798618", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:03:15.540630", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9684367775917053, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:03:15.678775", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9852951172936218, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:03:15.687604", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:03:15.784980", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6964793801307678, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:03:15.872006", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4387339619603535, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:03:15.886095", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997707009315491, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:03:16.037708", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999552965164185, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:03:16.129562", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:03:16.138912", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:06:13.918350", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6922712326049805, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:06:14.257847", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7191731426900206, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:06:14.264002", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:06:14.325225", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7960817813873291, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:06:14.388176", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3755395634757442, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:06:14.397910", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9996691942214966, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:06:14.479243", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999943971633911, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:06:14.545690", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:06:14.550773", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:09:13.830517", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9944975972175598, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:09:13.924163", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.999642857142857, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:09:13.929384", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:09:13.989660", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8311362862586975, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:09:14.054931", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3147194047288313, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:09:14.064723", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999677300453186, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:09:14.143351", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999943971633911, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:09:14.208496", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:09:14.214088", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:12:13.844791", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7500609755516052, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:12:14.536840", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:12:14.541951", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:12:14.601929", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8326660394668579, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:12:14.666506", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.36705099816655, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:12:14.679863", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998568296432495, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:12:14.758648", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:12:14.825286", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:12:14.832976", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:15:13.848315", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9711716771125793, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:15:13.928160", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:15:13.933821", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:15:13.991541", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8395078778266907, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:15:14.070308", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.46084324933134463, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:15:14.079995", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999538660049438, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:15:14.160452", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:15:14.239949", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:15:14.245053", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:18:13.801919", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7971259355545044, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:18:13.945962", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9802589285714286, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:18:13.951072", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:18:14.010295", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8451873064041138, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:18:14.072141", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4353181973465756, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:18:14.096783", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998892545700073, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:18:14.176755", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999468326568604, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:18:14.240938", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:18:14.257664", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:21:13.970156", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9746691584587097, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:21:14.035525", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7901091987816571, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:21:14.041154", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:21:14.100404", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8446332812309265, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:21:14.324942", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5202530087715312, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:21:14.334688", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998437166213989, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:21:14.414250", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999935626983643, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:21:14.482122", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:21:14.487193", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:24:13.967045", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5286632776260376, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:24:14.031981", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:24:14.037607", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:24:14.097355", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8384325504302979, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:24:14.160798", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4032186907573467, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:24:14.171493", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999487400054932, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:24:14.248879", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:24:14.316231", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:24:14.321323", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:27:13.931422", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.867353081703186, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:27:13.997253", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:27:14.002364", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:27:14.063669", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7954366207122803, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:27:14.127077", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4834763759158431, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:27:14.136271", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999614953994751, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:27:14.216733", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:27:14.283045", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:27:14.288165", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:30:13.842558", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9352155923843384, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:30:14.440042", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:30:14.445181", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:30:14.504229", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7219842672348022, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:30:14.568958", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5752512683418057, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:30:14.578210", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999963641166687, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:30:14.657209", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:30:14.727254", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.705, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:30:14.732886", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:33:13.829050", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7975873351097107, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:33:14.176442", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9862054057054057, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:33:14.181580", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:33:14.243252", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7941638827323914, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:33:14.305769", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8336725031711025, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:33:14.315961", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999950647354126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:33:14.417147", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999521970748901, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:33:14.480429", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.87, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:33:14.507490", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:36:13.771356", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9920870661735535, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:36:14.064507", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.5436791770503561, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:36:14.070132", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:36:14.129159", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.816246509552002, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:36:14.194151", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8477254943646301, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:36:14.210532", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999674558639526, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:36:14.288398", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999899864196777, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:36:14.355264", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.765, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:36:14.365966", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:39:13.792305", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8348205089569092, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:39:14.504756", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9924285714285714, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:39:14.509862", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:39:14.568681", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8137514591217041, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:39:14.631112", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8107493476886176, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:39:14.653618", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999740123748779, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:39:14.733084", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999624490737915, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:39:14.799925", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.75, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:39:14.808632", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:42:13.793483", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9475431442260742, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:42:14.108631", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:42:14.114248", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:42:14.173567", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.813364565372467, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:42:14.238579", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8035952332845032, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:42:14.248823", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999613761901855, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:42:14.328377", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999620914459229, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:42:14.397290", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.75, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:42:14.402990", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:45:13.909670", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9619229435920715, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:45:13.974951", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:45:13.981133", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:45:14.040945", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8255719542503357, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:45:14.103444", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.887562160134626, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:45:14.113668", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999651908874512, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:45:14.198355", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:45:14.264186", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.75, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:45:14.269315", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:48:13.936859", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.799231767654419, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:48:14.004872", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.931512481717629, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:48:14.009976", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:48:14.069865", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8205118775367737, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:48:14.134895", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5839862993259213, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:48:14.145133", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999743700027466, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:48:14.223623", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999956488609314, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:48:14.291964", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:48:14.297610", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:51:13.961518", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5607553124427795, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:51:14.027901", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.6924125576160882, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:51:14.033027", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:51:14.095538", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7899478077888489, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:51:14.159506", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8221659308153534, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:51:14.169702", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998772144317627, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:51:14.249623", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999780654907227, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:51:14.320481", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:51:14.326107", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:54:13.806884", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9448075294494629, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:54:13.874698", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:54:13.880305", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:54:13.940211", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7046007513999939, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:54:14.002697", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.35005536359626754, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:54:14.012385", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9979578256607056, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:54:14.091440", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999972581863403, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:54:14.158288", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:54:14.163401", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T20:57:13.763156", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9596941471099854, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T20:57:14.384541", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T20:57:14.389731", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T20:57:14.448039", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7520871162414551, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T20:57:14.511488", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.436546128653838, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T20:57:14.521703", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9993318915367126, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T20:57:14.600585", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999977350234985, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T20:57:14.668182", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T20:57:14.673249", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:00:13.772201", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9660834670066833, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:00:14.900130", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9988035714285713, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:00:14.905789", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:00:14.966202", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7904788851737976, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:00:15.030985", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.3454588672496772, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:00:15.040778", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9992392063140869, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:00:15.119302", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999979734420776, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:00:15.186154", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:00:15.191277", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:03:13.816081", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.6831386685371399, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:03:14.053904", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8941447857697858, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:03:14.059005", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:03:14.117927", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8002626299858093, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:03:14.180921", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8607493018515112, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:03:14.190615", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999159574508667, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:03:14.270113", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999339580535889, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:03:14.336449", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.59, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:03:14.341562", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:06:13.888785", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9831599593162537, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:06:13.968027", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.6241388732443028, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:06:13.973148", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:06:14.033859", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8155399560928345, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:06:14.098378", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8898700925209773, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:06:14.108632", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999685287475586, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:06:14.189676", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999676942825317, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:06:14.261179", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.58, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:06:14.266273", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:09:14.016738", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7207076549530029, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:09:14.120298", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9918293650793651, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:09:14.125490", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:09:14.191786", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8176340460777283, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:09:14.257826", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9277376318351114, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:09:14.276748", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999768733978271, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:09:14.365373", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999632835388184, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:09:14.436032", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:09:14.447910", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:12:13.791205", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8778249025344849, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:12:14.469293", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:12:14.474399", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:12:14.534810", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8216647505760193, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:12:14.600857", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9152454236289048, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:12:14.610544", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999696016311646, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:12:14.692137", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999632835388184, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:12:14.759477", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.57, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:12:14.764582", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:15:13.966387", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8914534449577332, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:15:14.034516", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:15:14.040133", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:15:14.101291", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8206003904342651, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:15:14.166338", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9254847489830628, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:15:14.176590", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999968409538269, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:15:14.257151", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:15:14.325579", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.57, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:15:14.331215", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:18:14.636581", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7423037886619568, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:18:14.799798", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.8849342532467532, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:18:14.805405", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:18:14.868033", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.728439450263977, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:18:14.933549", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4675369335206138, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:18:14.943755", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999758005142212, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:18:15.024412", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999558925628662, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:18:15.094600", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.575, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:18:15.100785", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:21:13.840944", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7633333802223206, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:21:14.506150", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9379547258297259, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:21:14.511279", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:21:14.575955", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8228104114532471, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:21:14.640349", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.4715587649668185, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:21:14.650554", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999639987945557, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:21:14.733612", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999581575393677, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:21:14.802617", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:21:14.808228", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:24:13.968331", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7883781790733337, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:24:14.218842", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9971073008849558, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:24:15.061039", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:24:15.135736", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6418359875679016, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:24:15.201513", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.45019169187979813, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:24:15.214316", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999717473983765, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:24:15.296925", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999732971191406, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:24:15.369703", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:24:15.375312", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:27:13.937802", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.6347262263298035, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:27:14.002622", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:27:14.008192", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:27:14.068906", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7203804850578308, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:27:14.151271", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.43703486812849385, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:27:14.161456", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999654293060303, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:27:14.239499", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:27:14.306961", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:27:14.312059", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:30:13.879785", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5464563369750977, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:30:13.956849", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:30:14.557600", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:30:14.628881", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7325376272201538, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:30:14.691951", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.33530816255197476, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:30:14.703159", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999669790267944, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:30:14.783645", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:30:14.851963", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.585, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:30:14.857075", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:33:13.790042", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7194412350654602, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:33:14.155319", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.973006813613038, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:33:14.160462", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:33:14.220711", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5479848980903625, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:33:14.285118", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4953717386163973, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:33:14.294278", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998395442962646, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:33:14.374591", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999513626098633, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:33:14.440813", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:33:14.445868", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:36:13.775950", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9896134734153748, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:36:14.180325", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7568596224851841, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:36:14.185903", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:36:14.248431", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5294607281684875, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:36:14.310890", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.395005187084165, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:36:14.321166", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998655319213867, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:36:14.399824", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999927282333374, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:36:14.467334", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:36:14.473066", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:39:13.768986", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9821027517318726, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:39:14.194036", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9937521062271063, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:39:14.199150", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:39:14.264660", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8379412293434143, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:39:14.329634", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.757598670975936, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:39:14.339854", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999850869178772, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:39:14.420926", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999939203262329, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:39:14.486789", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:39:14.492405", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:42:13.840773", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9947636127471924, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:42:13.909404", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.998775, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:42:13.914536", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:42:13.978710", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8292239904403687, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:42:14.042721", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.7605986709759359, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:42:14.053045", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998810291290283, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:42:14.131033", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999994158744812, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:42:14.197862", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:42:14.203481", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:45:13.968238", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9970832467079163, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:45:14.035353", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9925580687830688, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:45:14.040465", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:45:14.100354", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8268288969993591, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:45:14.163295", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.8467284526863823, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:45:14.173039", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998379945755005, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:45:14.253909", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999938011169434, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:45:14.320704", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:45:14.326853", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:48:13.949138", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9831664562225342, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:48:14.015095", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9898250360750361, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:48:14.142351", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:48:14.202695", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8299264907836914, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:48:14.268255", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6525893679034415, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:48:14.277980", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998970031738281, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:48:14.359549", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999500513076782, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:48:14.426467", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.595, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:48:14.431595", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:51:13.791721", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9519432783126831, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:51:14.761946", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.5960839720151035, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:51:14.767547", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:51:14.827123", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8424426317214966, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:51:14.895269", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5477335746772829, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:51:14.904506", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998173117637634, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:51:14.984412", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999942779541016, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:51:15.050247", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.595, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:51:15.055865", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:54:13.745633", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9767065048217773, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:54:13.920485", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9945337301587301, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:54:13.925585", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:54:13.985423", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8339825868606567, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:54:14.049873", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.48268599128945255, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:54:14.059105", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998192191123962, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:54:14.138105", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999990701675415, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:54:14.205996", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.595, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:54:14.211633", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T21:57:13.772318", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9821229577064514, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T21:57:13.962999", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T21:57:13.968100", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T21:57:14.025356", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7326303124427795, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T21:57:14.087758", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7085496939405179, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T21:57:14.111758", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999583959579468, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T21:57:14.191287", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999728202819824, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T21:57:14.258076", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T21:57:14.263675", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:00:13.977676", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9920263886451721, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:00:14.044943", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:00:14.050563", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:00:14.112161", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7316306233406067, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:00:14.177121", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7117938528959413, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:00:14.187398", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999586343765259, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:00:14.265349", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:00:14.335877", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:00:14.341505", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:03:13.767240", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.5429482460021973, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:03:13.839398", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9808516898886637, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:03:13.845028", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:03:13.905638", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6964264512062073, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:03:13.970218", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4116841078720601, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:03:13.979951", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999276399612427, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:03:14.060423", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999644756317139, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:03:14.127341", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:03:14.132454", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:06:13.929100", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7293455004692078, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:06:14.477389", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9992857142857143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:06:14.482492", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:06:14.541542", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6913583278656006, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:06:14.605024", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.3379621434214165, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:06:14.615296", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999200105667114, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:06:14.692127", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999762773513794, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:06:14.757980", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:06:14.763090", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:09:13.807034", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.2548644244670868, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:09:13.887585", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:09:13.892654", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:09:13.951388", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6623988151550293, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:09:14.014165", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5226636285715165, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:09:14.037069", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9789880514144897, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:09:14.120991", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999898672103882, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:09:14.187295", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:09:14.201030", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:12:13.819690", "model_name": "short_term_pattern_nn", "signal": 1, "confidence": 0.254583477973938, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:12:13.970853", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:12:14.836933", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:12:14.904274", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7977844476699829, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:12:14.967159", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.670685968774127, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:12:14.977478", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8644046783447266, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:12:15.078675", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999991774559021, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:12:15.142136", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:12:15.168633", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:15:13.772094", "model_name": "short_term_pattern_nn", "signal": -1, "confidence": 0.25530925393104553, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:15:14.177002", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:15:14.182106", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:15:14.239779", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.789671778678894, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:15:14.304232", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6532688166213748, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:15:14.325745", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.8924121260643005, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:15:14.403061", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999924898147583, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:15:14.469347", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:15:14.481121", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:18:13.823816", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9734885096549988, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:18:13.910234", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9946875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:18:13.915383", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:18:13.974642", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8096920251846313, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:18:14.040100", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8541970673213354, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:18:14.051919", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999220371246338, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:18:14.131857", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999316930770874, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:18:14.203204", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:18:14.208331", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:21:13.865764", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9681158661842346, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:21:13.933110", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9852491610456727, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:21:13.938215", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:21:13.997577", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8160864114761353, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:21:14.063181", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8337566343145238, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:21:14.073388", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998724460601807, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:21:14.152869", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999438524246216, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:21:14.220223", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:21:14.225327", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:24:14.008068", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.7343945503234863, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:24:14.073416", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.990345238095238, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:24:14.078501", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:24:14.144067", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8174570798873901, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:24:14.208191", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8604847407985988, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:24:14.218970", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999359846115112, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:24:14.300467", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999619722366333, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:24:14.366861", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:24:14.372011", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:27:14.038350", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9498118162155151, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:27:14.104828", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:27:14.110500", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:27:14.172742", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8270884156227112, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:27:14.237252", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8988801916386727, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:27:14.248020", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999700784683228, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:27:14.331337", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999634027481079, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:27:14.397879", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:27:14.402987", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:30:14.008728", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9726538062095642, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:30:14.196964", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9972321428571428, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:30:14.229308", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:30:14.289603", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8280163407325745, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:30:14.356759", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9126493485160835, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:30:14.366946", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999942421913147, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:30:14.449119", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999626874923706, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:30:14.515101", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.63, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:30:14.520192", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:33:14.018123", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9275023937225342, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:33:14.084634", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.936329961704962, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:33:14.089783", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:33:14.150014", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8236480951309204, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:33:14.214409", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.6645812394538549, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:33:14.224647", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999525547027588, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:33:14.305442", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999526739120483, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:33:14.372965", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.635, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:33:14.378041", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:36:14.016878", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9163730144500732, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:36:14.085467", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9762613849328967, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:36:14.090602", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:36:14.151484", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8253311514854431, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:36:14.214947", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7620674508073338, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:36:14.225166", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998308420181274, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:36:14.307198", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999525547027588, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:36:14.375164", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.635, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:36:14.380298", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:39:14.021124", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9895787835121155, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:39:14.089254", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9892302754435106, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:39:14.094875", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:39:14.154807", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7930466532707214, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:39:14.217839", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5059050110108965, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:39:14.227624", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999499320983887, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:39:14.306247", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999727010726929, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:39:14.374206", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.635, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:39:14.379319", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:42:14.013628", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9879814982414246, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:42:14.080231", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:42:14.085395", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:42:14.145581", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7939287424087524, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:42:14.207826", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5992751763878068, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:42:14.218019", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999723434448242, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:42:14.298919", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:42:14.365358", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.635, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:42:14.370489", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:45:13.969023", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9879193305969238, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:45:14.657662", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:45:14.662776", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:45:14.723083", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7941442728042603, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:45:14.787243", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5942088201250221, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:45:14.797016", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999464750289917, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:45:14.877226", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999972939491272, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:45:14.943618", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.635, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:45:14.948736", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:48:13.749458", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9891698956489563, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:48:14.782444", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9708859085829425, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:48:14.787058", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:48:14.846956", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7212937474250793, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:48:14.909763", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6190086464908277, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:48:14.919487", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999023675918579, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:48:14.996927", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999539852142334, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:48:15.063790", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:48:15.069375", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:51:13.810310", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8254395127296448, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:51:13.924888", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.6955114037424641, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:51:14.501804", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:51:14.568714", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.798574686050415, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:51:14.632230", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5513193711806682, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:51:14.644033", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998784065246582, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:51:14.743311", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999954700469971, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:51:14.807104", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:51:14.833154", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:54:13.876434", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9923211932182312, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:54:13.953152", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9848374183006534, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:54:13.958241", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:54:14.019132", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7927498817443848, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:54:14.081545", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.48052270449471135, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:54:14.091754", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999622106552124, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:54:14.170190", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999734163284302, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:54:14.240573", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:54:14.245676", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T22:57:13.851293", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9911318421363831, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T22:57:13.917641", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T22:57:13.922773", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T22:57:13.987231", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7932677865028381, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T22:57:14.051725", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6091237880689395, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T22:57:14.061477", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999614953994751, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T22:57:14.141955", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999735355377197, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T22:57:14.209334", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T22:57:14.214469", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:00:13.818200", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9909310340881348, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:00:14.188243", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:00:14.193306", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:00:14.253437", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.743197500705719, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:00:14.317914", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.6053737880689396, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:00:14.328160", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999614953994751, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:00:14.407768", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999738931655884, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:00:14.475289", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:00:14.480942", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:03:13.778255", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9307498335838318, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:03:13.874172", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.98978373015873, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:03:14.217754", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:03:14.298476", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6240975856781006, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:03:14.359215", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.5307871597723602, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:03:14.386772", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9997485280036926, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:03:14.467264", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999957799911499, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:03:14.534207", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:03:14.549520", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:06:13.861853", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8126737475395203, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:06:14.027630", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.7720210638506434, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:06:14.032761", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:06:14.091061", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.605703592300415, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:06:14.153519", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4845544077786706, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:06:14.173969", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998680353164673, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:06:14.251935", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999947547912598, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:06:14.317872", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:06:14.334734", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:09:14.000007", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9460825324058533, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:09:14.068669", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9830365080230423, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:09:14.074306", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:09:14.133352", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.700552761554718, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:09:14.197003", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.38048666831057504, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:09:14.206640", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9987168312072754, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:09:14.284273", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999984502792358, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:09:14.353313", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:09:14.358400", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:12:13.798098", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9866132140159607, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:12:13.875226", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9992857142857143, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:12:13.880443", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:12:13.940648", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7254089117050171, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:12:14.002335", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.38890877405084273, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:12:14.012571", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.99931800365448, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:12:14.089729", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999983310699463, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:12:14.156223", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:12:14.161395", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:15:13.769492", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9926453828811646, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:15:14.550239", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9963690476190475, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:15:14.555373", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:15:14.614667", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.700327455997467, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:15:14.678165", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.4666630762981997, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:15:14.687878", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999538660049438, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:15:14.767328", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999980092048645, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:15:14.835828", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:15:14.841434", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:18:13.820002", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5666754841804504, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:18:14.559696", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9952777777777777, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:18:14.564838", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:18:14.623075", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.6315639615058899, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:18:14.685096", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.44781628266596696, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:18:14.711202", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999344348907471, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:18:14.788699", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999649524688721, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:18:14.854102", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:18:14.866850", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:21:13.804044", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9977795481681824, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:21:14.428219", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.6669935572137763, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:21:14.433371", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:21:14.495521", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.5871438980102539, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:21:14.560623", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.44065473393861276, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:21:14.570344", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999152421951294, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:21:14.649012", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999974966049194, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:21:14.717918", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:21:14.723031", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:24:13.974463", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9922471046447754, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:24:14.039859", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9798661495911495, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:24:14.044943", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:24:14.105884", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8386871814727783, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:24:14.171887", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4143142520919049, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:24:14.182034", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999277591705322, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:24:14.261173", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999854564666748, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:24:14.329369", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:24:14.334434", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:27:13.873636", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9956607222557068, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:27:14.135299", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9931830687830688, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:27:14.140432", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:27:14.199734", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8335514068603516, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:27:14.265766", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4213705268897147, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:27:14.275524", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9998635053634644, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:27:14.352959", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999868869781494, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:27:14.419833", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.61, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:27:14.424933", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:30:13.811896", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9950470924377441, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:30:14.569008", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9950249999999999, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:30:14.574118", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:30:14.634464", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8332286477088928, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:30:14.701510", "model_name": "medium_term_breakout_rf", "signal": -1, "confidence": 0.4265333012206333, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:30:14.712195", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999962329864502, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:30:14.791634", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999987006187439, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:30:14.858545", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.62, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:30:14.863665", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:33:13.762803", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8043262958526611, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:33:14.565804", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9685984954587896, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:33:14.570930", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:33:14.630377", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7565178275108337, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:33:14.693328", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7117237864822209, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:33:14.703570", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.999922513961792, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:33:14.783547", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999505281448364, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:33:14.851921", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:33:14.857020", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:36:13.823479", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.9554191827774048, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:36:13.887877", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.5061377554765961, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:36:13.893004", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:36:13.950244", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.813109278678894, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:36:14.024907", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7099654617860506, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:36:14.035135", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999688863754272, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:36:14.114643", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999902248382568, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:36:14.182515", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:36:14.187649", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:39:13.800727", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.5577107071876526, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:39:13.888127", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9944754273504273, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:39:13.893241", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:39:13.952994", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8109506368637085, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:39:14.016967", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.7242398223872242, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:39:14.026183", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999786615371704, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:39:14.105432", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999618530273438, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:39:14.171473", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:39:14.177589", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:42:13.794451", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8883134126663208, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:42:14.027985", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9984615384615384, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:42:14.033653", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:42:14.092969", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8134161829948425, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:42:14.156379", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.8028113910690858, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:42:14.166075", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999586343765259, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:42:14.246716", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999961256980896, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:42:14.312555", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.6, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:42:14.317687", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:45:13.784463", "model_name": "short_term_pattern_nn", "signal": 2, "confidence": 0.8726968169212341, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:45:14.618695", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 1.0, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:45:14.623821", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:45:14.691391", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.823652982711792, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:45:14.754831", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.9177404594085351, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:45:14.765048", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999696016311646, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:45:14.851736", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999629259109497, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:45:14.919515", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:45:14.924659", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:48:13.763612", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.7313324809074402, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:48:14.188438", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9652604016354018, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:48:14.193576", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:48:14.250771", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8227885365486145, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:48:14.313731", "model_name": "medium_term_breakout_rf", "signal": 1, "confidence": 0.35973649955997467, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:48:14.335735", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999760389328003, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:48:14.414085", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999581575393677, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:48:14.479891", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:48:14.512576", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:51:13.955390", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9008148908615112, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:51:14.020978", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9784924708293914, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:51:14.026067", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:51:14.085934", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.8236414194107056, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:51:14.149350", "model_name": "medium_term_breakout_rf", "signal": 2, "confidence": 0.5112878771278137, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:51:14.159084", "model_name": "medium_term_volatility_xgb", "signal": 0, "confidence": 0.9999793767929077, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:51:14.242603", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.999957799911499, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:51:14.321123", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:51:14.326243", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:54:13.803908", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.8253234028816223, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:54:14.428061", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.9954761904761904, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:54:14.433171", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:54:14.493031", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7403262853622437, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:54:14.557034", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.44353978873332683, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:54:14.566749", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.9967735409736633, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:54:14.648769", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999872446060181, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:54:14.717164", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:54:14.722803", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
{"timestamp": "2025-07-08T23:57:13.824458", "model_name": "short_term_pattern_nn", "signal": -2, "confidence": 0.9519891142845154, "timeframe_category": "short_term", "weight": 1.0, "features_used": 21, "model_purpose": "Pattern recognition for scalping with trend direction awareness"}
{"timestamp": "2025-07-08T23:57:14.419722", "model_name": "short_term_momentum_rf", "signal": 0, "confidence": 0.99875, "timeframe_category": "short_term", "weight": 1.0, "features_used": 19, "model_purpose": "Momentum detection for quick trades with trend direction filtering"}
{"timestamp": "2025-07-08T23:57:14.424840", "model_name": "short_term_reversion_gb", "signal": 1, "confidence": 0.9999998524274585, "timeframe_category": "short_term", "weight": 1.0, "features_used": 15, "model_purpose": "Mean reversion scalping with trend context"}
{"timestamp": "2025-07-08T23:57:14.485524", "model_name": "medium_term_trend_lstm", "signal": 0, "confidence": 0.7441432476043701, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 19, "model_purpose": "Trend continuation detection with multi-timeframe analysis"}
{"timestamp": "2025-07-08T23:57:14.551507", "model_name": "medium_term_breakout_rf", "signal": 0, "confidence": 0.38634365452941216, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 17, "model_purpose": "Breakout detection with trend direction confirmation"}
{"timestamp": "2025-07-08T23:57:14.561368", "model_name": "medium_term_volatility_xgb", "signal": 1, "confidence": 0.9863331913948059, "timeframe_category": "medium_term", "weight": 0.5, "features_used": 16, "model_purpose": "Volatility trading with trend-aware positioning"}
{"timestamp": "2025-07-08T23:57:14.639047", "model_name": "long_term_macro_dnn", "signal": 0, "confidence": 0.9999880790710449, "timeframe_category": "long_term", "weight": 0.1, "features_used": 12, "model_purpose": "Major trend analysis with macro direction awareness"}
{"timestamp": "2025-07-08T23:57:14.705144", "model_name": "long_term_levels_rf", "signal": 0, "confidence": 0.605, "timeframe_category": "long_term", "weight": 0.1, "features_used": 13, "model_purpose": "Support/resistance trading with trend-aware levels"}
{"timestamp": "2025-07-08T23:57:14.710290", "model_name": "long_term_portfolio_gb", "signal": 0, "confidence": 0.9999997306826254, "timeframe_category": "long_term", "weight": 0.1, "features_used": 16, "model_purpose": "Portfolio allocation optimization with trend-based sizing"}
